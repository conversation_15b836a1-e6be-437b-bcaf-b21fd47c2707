// Admin Currency Management Models and Interfaces

import { ApiResponse } from '../../../../api/axiosInstance';

// Base pagination interface
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginationResponse<T> {
  total: number;
  skip: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
  items: T[];
}

// Currency conversion interface
export interface CurrencyConversion {
  id: number;
  from_currency: string;
  code: string;
  conversion_rate: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

// Create currency conversion request interface
export interface CreateCurrencyConversionRequest {
  from_currency: string;
  code: string;
  conversion_rate: number;
  is_active?: boolean;
}

// Update currency conversion request interface
export interface UpdateCurrencyConversionRequest {
  from_currency?: string;
  code?: string;
  conversion_rate?: number;
  is_active?: boolean;
}

// API Response interfaces
export interface GetCurrencyConversionsResponse extends ApiResponse<PaginationResponse<CurrencyConversion>> {}
export interface GetCurrencyConversionResponse extends ApiResponse<CurrencyConversion> {}
export interface CreateCurrencyConversionResponse extends ApiResponse<CurrencyConversion> {}
export interface UpdateCurrencyConversionResponse extends ApiResponse<CurrencyConversion> {}
export interface DeleteCurrencyConversionResponse extends ApiResponse<{ message: string }> {}

// Form validation errors
export interface CurrencyConversionFormErrors {
  from_currency?: string;
  code?: string;
  conversion_rate?: string;
  is_active?: string;
  general?: string;
}

// Currency conversion filters for search/filtering
export interface CurrencyConversionFilters extends PaginationParams {
  search?: string;
  from_currency?: string;
  code?: string;
  is_active?: boolean;
  created_from?: string;
  created_to?: string;
}

// Currency statistics interface
export interface CurrencyStatistics {
  total_conversions: number;
  active_conversions: number;
  inactive_conversions: number;
  supported_currencies: string[];
  most_used_currencies: Array<{
    currency: string;
    usage_count: number;
  }>;
  recent_updates: number;
}

// Currency rate update interface
export interface CurrencyRateUpdate {
  currency_pair: string;
  old_rate: number;
  new_rate: number;
  change_percentage: number;
  updated_at: string;
  updated_by?: string;
}

// Bulk currency update interface
export interface BulkCurrencyUpdateRequest {
  updates: Array<{
    id: number;
    conversion_rate: number;
  }>;
}

// Currency exchange rate from external API
export interface ExternalExchangeRate {
  from: string;
  to: string;
  rate: number;
  timestamp: string;
  source: string;
}

// Currency pair interface
export interface CurrencyPair {
  from_currency: string;
  to_currency: string;
  rate: number;
  is_active: boolean;
}

// Supported currencies list
export interface SupportedCurrency {
  code: string;
  name: string;
  symbol: string;
  is_fiat: boolean;
  is_crypto: boolean;
  is_active: boolean;
  decimal_places: number;
}
