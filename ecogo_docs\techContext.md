# Technical Context

## Technology Stack

### Core Technologies
- **React**: 19.1.0 (Latest version with concurrent features)
- **TypeScript**: 4.9.5 (Type safety and better developer experience)
- **Node.js**: Version 16+ (Based on @types/node version)

### Frontend Dependencies
- **React Router DOM**: 7.5.3 (Client-side routing)
- **Axios**: 1.9.0 (HTTP client for API calls)
- **Lucide React**: 0.503.0 (Icon library)
- **ECharts**: 5.6.0 (Data visualization and charts)

### Styling & UI
- **Tailwind CSS**: 3.3.0 (Utility-first CSS framework)
- **SASS**: 1.89.0 (CSS preprocessor for custom styles)
- **PostCSS**: 8.4.23 (CSS post-processing)
- **Autoprefixer**: 10.4.14 (CSS vendor prefixing)

### Development Tools
- **React Scripts**: 5.0.1 (Build tooling and development server)
- **TypeScript**: 4.9.5 (Static type checking)
- **Web Vitals**: 2.1.4 (Performance monitoring)

### Testing Framework
- **Jest**: Via React Scripts (Unit testing)
- **React Testing Library**: 16.3.0 (Component testing)
- **Testing Library DOM**: 10.4.0 (DOM testing utilities)
- **Testing Library User Event**: 13.5.0 (User interaction testing)
- **Testing Library Jest DOM**: 6.6.3 (Custom Jest matchers)

## Development Environment

### Build System
- **Create React App**: Bootstrapped with CRA for standard React setup
- **Webpack**: Bundled via React Scripts
- **Babel**: Transpilation handled by React Scripts
- **ESLint**: Code linting with react-app configuration

### Scripts Available
```json
{
  "start": "react-scripts start",      // Development server
  "build": "react-scripts build",     // Production build
  "test": "react-scripts test",       // Test runner
  "eject": "react-scripts eject"      // Eject from CRA (not recommended)
}
```

### Browser Support
- **Production**: >0.2%, not dead, not op_mini all
- **Development**: Latest Chrome, Firefox, Safari

## API Configuration

### Backend Services
- **Main API**: http://***********:8003 (Primary application API)
- **Auth API**: http://***********:8002 (Authentication service)
- **Timeout**: 30 seconds default
- **Max File Size**: 10MB for uploads
- **Retry Attempts**: 3 attempts for failed requests

### Authentication
- **JWT Tokens**: Access and refresh token pattern
- **Storage**: Local storage for token persistence
- **Auto-refresh**: Automatic token refresh on expiry
- **Security**: Token validation and expiry checking

## Development Setup

### Prerequisites
- Node.js 16+ 
- npm or yarn package manager
- Modern web browser

### Installation
```bash
npm install          # Install dependencies
npm start           # Start development server
npm run build       # Build for production
npm test            # Run tests
```

### Environment Variables
- `NEXT_PUBLIC_API_BASE_URL`: Main API base URL
- `NEXT_PUBLIC_AUTH_API_BASE_URL`: Auth API base URL
- `NEXT_PUBLIC_API_TIMEOUT`: API request timeout
- `NEXT_PUBLIC_MAX_FILE_SIZE`: Maximum file upload size
- `NEXT_PUBLIC_RETRY_ATTEMPTS`: Number of retry attempts

## File Structure Conventions

### Import Aliases
- `@components/*`: Maps to `src/components/*`
- Relative imports for local files
- Absolute imports for external packages

### File Naming
- **Components**: PascalCase (e.g., `UserDashboard.tsx`)
- **Services**: camelCase (e.g., `authService.ts`)
- **Models**: camelCase with .model suffix (e.g., `user.model.ts`)
- **Styles**: kebab-case (e.g., `header.scss`)

### Code Organization
- One component per file
- Co-located styles with components
- Separate service files for API logic
- Shared models in dedicated folder
- Utility functions in separate files

## Performance Considerations
- React 19 concurrent features for better UX
- Code splitting opportunities (not yet implemented)
- Image optimization needs
- Bundle size optimization potential
- Lazy loading for routes (not yet implemented)
