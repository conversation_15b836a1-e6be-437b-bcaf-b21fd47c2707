import { Edit, Flag, Search, ToggleLeft, ToggleRight, ChevronDown } from 'lucide-react'
import React, { useState } from 'react'

interface Currency {
  id: number
  currency: string
  code: string
  flag: string
  rate: string
  lastUpdated: string
  status: string
  statusColor: 'green' | 'yellow' | 'red'
  isActive: boolean
}

const AdminCurrency: React.FC = () => {
  const [selectedCurrency, setSelectedCurrency] = useState<string>('Select Currency')
  const [exchangeRate, setExchangeRate] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false)

  const [currencies, setCurrencies] = useState<Currency[]>([
    {
      id: 1,
      currency: "US Dollar",
      code: "USD",
      flag: "us",
      rate: "1.0000",
      lastUpdated: "Apr 29, 2025 - 10:00 AM",
      status: "Active",
      statusColor: "green",
      isActive: true,
    },
    {
      id: 2,
      currency: "Indian Rupee",
      code: "INR",
      flag: "in",
      rate: "82.4530",
      lastUpdated: "Apr 29, 2025 - 09:45 AM",
      status: "Active",
      statusColor: "green",
      isActive: true,
    },
    {
      id: 3,
      currency: "UAE Dirham",
      code: "AED",
      flag: "ae",
      rate: "3.6725",
      lastUpdated: "Apr 29, 2025 - 09:30 AM",
      status: "Active",
      statusColor: "green",
      isActive: true,
    },
    {
      id: 4,
      currency: "Euro",
      code: "EUR",
      flag: "eu",
      rate: "0.9150",
      lastUpdated: "Apr 29, 2025 - 09:15 AM",
      status: "Active",
      statusColor: "green",
      isActive: true,
    },
    {
      id: 5,
      currency: "British Pound",
      code: "GBP",
      flag: "gb",
      rate: "0.7950",
      lastUpdated: "Apr 29, 2025 - 09:00 AM",
      status: "Active",
      statusColor: "green",
      isActive: true,
    },
  ])

  const availableCurrencies = [
    { name: "Japanese Yen", code: "JPY" },
    { name: "Canadian Dollar", code: "CAD" },
    { name: "Australian Dollar", code: "AUD" },
    { name: "Swiss Franc", code: "CHF" },
    { name: "Chinese Yuan", code: "CNY" },
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedCurrency !== 'Select Currency' && exchangeRate) {
      // Handle adding new currency
      console.log('Adding currency:', { selectedCurrency, exchangeRate })
      setSelectedCurrency('Select Currency')
      setExchangeRate('')
    }
  }

  const toggleCurrencyStatus = (id: number) => {
    setCurrencies(currencies.map(currency => 
      currency.id === id 
        ? { 
            ...currency, 
            isActive: !currency.isActive,
            status: !currency.isActive ? 'Active' : 'Inactive',
            statusColor: !currency.isActive ? 'green' : 'red' as 'green' | 'red'
          }
        : currency
    ))
  }

  const getStatusBadgeClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-emerald-900/50 text-emerald-300 border border-emerald-800'
      case 'yellow':
        return 'bg-yellow-900/50 text-yellow-300 border border-yellow-800'
      case 'red':
        return 'bg-red-900/50 text-red-300 border border-red-800'
      default:
        return 'bg-gray-700/50 text-gray-300 border border-gray-600'
    }
  }

  const filteredCurrencies = currencies.filter(currency =>
    currency.currency.toLowerCase().includes(searchTerm.toLowerCase()) ||
    currency.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      {/* Add New Currency */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl mb-8">
        <div className="p-6 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-white mb-4">
            Add New Currency
          </h3>
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row items-end gap-4">
            <div className="w-full sm:w-48">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-left text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                >
                  <span className="flex items-center justify-between">
                    <span className={selectedCurrency === 'Select Currency' ? 'text-gray-400' : 'text-white'}>
                      {selectedCurrency}
                    </span>
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  </span>
                </button>
                
                {isDropdownOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-gray-700 border border-gray-600 rounded-lg shadow-lg">
                    {availableCurrencies.map((currency) => (
                      <button
                        key={currency.code}
                        type="button"
                        onClick={() => {
                          setSelectedCurrency(`${currency.name} (${currency.code})`)
                          setIsDropdownOpen(false)
                        }}
                        className="w-full px-4 py-2 text-left text-white hover:bg-gray-600 first:rounded-t-lg last:rounded-b-lg transition-colors"
                      >
                        {currency.name} ({currency.code})
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="flex-1 max-w-xs w-full">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                USDT Exchange Rate
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.0001"
                  min="0"
                  value={exchangeRate}
                  onChange={(e) => setExchangeRate(e.target.value)}
                  placeholder="Enter rate"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  USDT
                </span>
              </div>
            </div>

            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-colors whitespace-nowrap w-full sm:w-auto"
            >
              Add Currency
            </button>
          </form>
        </div>
      </div>

      {/* Currency List */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl">
        <div className="px-6 py-4 border-b border-gray-700/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h3 className="text-lg font-semibold text-white">
            Currency List
          </h3>
          <div className="w-full sm:w-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search currencies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-full sm:w-64"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-700/30">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  USDT Exchange Rate
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Last Updated
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50">
              {filteredCurrencies.map((currency) => (
                <tr key={currency.id} className="hover:bg-gray-700/30 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Flag className="mr-3 text-gray-400 w-5 h-5" />
                      <div className="text-sm font-medium text-white">
                        {currency.currency} ({currency.code})
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">
                      1 {currency.code} = {currency.rate} USDT
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-400">
                      {currency.lastUpdated}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClasses(currency.statusColor)}`}>
                      {currency.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-3">
                      <button className="text-blue-400 hover:text-blue-300 transition-colors p-1">
                        <Edit className="w-5 h-5" />
                      </button>
                      <button 
                        onClick={() => toggleCurrencyStatus(currency.id)}
                        className="transition-colors p-1"
                      >
                        {currency.isActive ? (
                          <ToggleRight className="w-6 h-6 text-emerald-400 hover:text-emerald-300" />
                        ) : (
                          <ToggleLeft className="w-6 h-6 text-gray-400 hover:text-gray-300" />
                        )}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-700/50 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-400">
            Showing <span className="font-medium text-white">1</span> to{" "}
            <span className="font-medium text-white">{filteredCurrencies.length}</span> of{" "}
            <span className="font-medium text-white">{currencies.length}</span> results
          </div>
          <div className="flex space-x-2">
            <button className="px-3 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 transition-colors">
              Previous
            </button>
            <button className="px-3 py-2 border border-blue-600 bg-blue-600/20 text-blue-400 rounded-lg text-sm font-medium">
              1
            </button>
            <button className="px-3 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 transition-colors">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminCurrency