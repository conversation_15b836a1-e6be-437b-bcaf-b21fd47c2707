import React, { useState } from 'react'
import { Save, FileText, Shield, Eye, Edit, Calendar } from 'lucide-react'

interface TermsSection {
  id: string
  title: string
  content: string
}

const AdminSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'terms' | 'privacy'>('terms')
  const [lastUpdated, setLastUpdated] = useState('May 3, 2025')
  const [isEditing, setIsEditing] = useState(false)
  
  const [termsContent, setTermsContent] = useState<TermsSection[]>([
    {
      id: 'introduction',
      title: '1. Introduction',
      content: `Welcome to Fruit-O-Cart. These Terms of Service ("Terms") govern your access to and use of our website, products, and services ("Services"). By accessing or using our Services, you agree to be bound by these Terms. If you do not agree to these Terms, please do not use our Services.

We may modify these Terms at any time. If we do so, we will notify you by publishing the modified Terms on this site. Your continued use of the Services after we publish the modified Terms constitutes your agreement to the modified Terms.`
    },
    {
      id: 'user-agreements',
      title: '2. User Agreements',
      content: `By creating an account and using our Services, you represent and warrant that:

• You are at least 18 years of age.
• You have the right, authority, and capacity to enter into these Terms and to abide by all of the terms and conditions set forth herein.
• You will not use the Services for any purpose that is unlawful or prohibited by these Terms.
• All information you provide to us is true, accurate, complete, and current, and you will maintain and update such information to keep it true, accurate, complete, and current.

You are responsible for safeguarding your password and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account.`
    },
    {
      id: 'acceptable-use',
      title: '3. Acceptable Use Policy',
      content: `When using our Services, you agree not to:

• Violate any applicable laws or regulations.
• Infringe upon the rights of others or violate their privacy or publicity rights.
• Use the Services to distribute unsolicited commercial messages ("spam").
• Upload or transmit viruses, malware, or other types of malicious software.
• Attempt to gain unauthorized access to our Services, user accounts, or computer systems.
• Engage in any activity that interferes with or disrupts the Services.
• Reproduce, duplicate, copy, sell, trade, resell or exploit any portion of the Services without our express written permission.

We reserve the right to terminate or suspend your access to the Services immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach these Terms.`
    }
  ])

  const [privacyContent, setPrivacyContent] = useState(`We are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, and safeguard your data when you use our services.

Information We Collect:
• Personal identification information (name, email address, phone number)
• Financial information for transactions
• Usage data and analytics
• Device and browser information

How We Use Your Information:
• To provide and maintain our services
• To process transactions and payments
• To communicate with you about your account
• To improve our services and user experience
• To comply with legal obligations

Data Security:
We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.`)

  const handleSaveTerms = () => {
    // Handle saving terms and conditions
    setLastUpdated(new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }))
    setIsEditing(false)
    console.log('Terms and conditions saved')
  }

  const handleSavePrivacy = () => {
    // Handle saving privacy policy
    setLastUpdated(new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }))
    setIsEditing(false)
    console.log('Privacy policy saved')
  }

  const updateTermsSection = (id: string, content: string) => {
    setTermsContent(prev => prev.map(section => 
      section.id === id ? { ...section, content } : section
    ))
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      {/* Header */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl mb-8">
        <div className="p-6 border-b border-gray-700/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600/20 p-2 rounded-lg">
                <FileText className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Settings</h1>
                <p className="text-gray-400">Manage terms, conditions, and privacy policies</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Calendar className="w-4 h-4" />
              <span>Last updated: {lastUpdated}</span>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-6 py-4">
          <div className="flex space-x-1 bg-gray-700/30 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('terms')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'terms'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
            >
              <FileText className="w-4 h-4" />
              <span>Terms & Conditions</span>
            </button>
            <button
              onClick={() => setActiveTab('privacy')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'privacy'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
            >
              <Shield className="w-4 h-4" />
              <span>Privacy Policy</span>
            </button>
          </div>
        </div>
      </div>

      {/* Content Area */}
      {activeTab === 'terms' && (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl">
          <div className="p-6 border-b border-gray-700/50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Terms & Conditions</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isEditing
                      ? 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isEditing ? <Eye className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
                  <span>{isEditing ? 'Preview' : 'Edit'}</span>
                </button>
                {isEditing && (
                  <button
                    onClick={handleSaveTerms}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    <span>Save Changes</span>
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="p-6 space-y-8">
            {termsContent.map((section) => (
              <div key={section.id} className="space-y-4">
                <h2 className="text-xl font-bold text-blue-400">{section.title}</h2>
                {isEditing ? (
                  <textarea
                    value={section.content}
                    onChange={(e) => updateTermsSection(section.id, e.target.value)}
                    className="w-full h-48 px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-vertical"
                    placeholder="Enter section content..."
                  />
                ) : (
                  <div className="text-gray-300 whitespace-pre-line leading-relaxed">
                    {section.content}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'privacy' && (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl">
          <div className="p-6 border-b border-gray-700/50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Privacy Policy</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isEditing
                      ? 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isEditing ? <Eye className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
                  <span>{isEditing ? 'Preview' : 'Edit'}</span>
                </button>
                {isEditing && (
                  <button
                    onClick={handleSavePrivacy}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    <span>Save Changes</span>
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="p-6">
            {isEditing ? (
              <textarea
                value={privacyContent}
                onChange={(e) => setPrivacyContent(e.target.value)}
                className="w-full h-96 px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-vertical"
                placeholder="Enter privacy policy content..."
              />
            ) : (
              <div className="text-gray-300 whitespace-pre-line leading-relaxed">
                {privacyContent}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminSettings
