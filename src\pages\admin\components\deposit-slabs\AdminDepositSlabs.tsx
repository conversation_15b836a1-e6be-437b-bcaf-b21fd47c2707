import { Edit, ToggleRight, Trash2, Search } from 'lucide-react'
import React, { useState } from 'react'

function AdminDepositSlabs() {
  const [depositAmount, setDepositAmount] = useState('')
  const [depositName, setDepositName] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  const handleSubmit = () => {
    // Handle form submission
    console.log('Adding deposit slab:', { depositAmount, depositName })
    setDepositAmount('')
    setDepositName('')
  }

  const depositSlabs = [
    {
      name: "Becath",
      amount: "2,500",
      date: "Apr 29, 2025",
      status: "Active",
      statusColor: "green",
    },
    {
      name: "Silver",
      amount: "5,000",
      date: "Apr 28, 2025",
      status: "Active",
      statusColor: "green",
    },
    {
      name: "Gold",
      amount: "10,000",
      date: "Apr 27, 2025",
      status: "Active",
      statusColor: "green",
    },
    {
      name: "Platinum",
      amount: "25,000",
      date: "Apr 26, 2025",
      status: "Inactive",
      statusColor: "red",
    },
    {
      name: "<PERSON>",
      amount: "50,000",
      date: "Apr 25, 2025",
      status: "Active",
      statusColor: "green",
    },
  ]

  const filteredSlabs = depositSlabs.filter(slab =>
    slab.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    slab.amount.includes(searchTerm)
  )

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Add New Deposit Slab Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg mb-6 border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-xl font-bold text-white mb-6">
            Add New Deposit Slab
          </h3>
          <div className="flex items-end gap-4">
            <div className="flex-1 max-w-xs">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Deposit Amount (USDT)
              </label>
              <input
                type="number"
                min="0"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                placeholder="Enter amount"
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
            </div>
            <div className="flex-1 max-w-xs">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Deposit Name
              </label>
              <input
                type="text"
                value={depositName}
                onChange={(e) => setDepositName(e.target.value)}
                placeholder="Enter name"
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
            </div>
            <button
              type="button"
              onClick={handleSubmit}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors whitespace-nowrap"
            >
              Add Deposit Slab
            </button>
          </div>
        </div>
      </div>

      {/* Deposit Slabs Table Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700">
        <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
          <h3 className="text-xl font-bold text-white">
            Deposit Slabs
          </h3>
          <div className="flex space-x-2">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search slabs..."
                className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm w-64"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-750">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Amount (USDT)
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Created Date
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-gray-800 divide-y divide-gray-700">
              {filteredSlabs.map((slab, index) => (
                <tr key={index} className="hover:bg-gray-700 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">
                      {slab.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">
                      ${slab.amount}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-400">
                      {slab.date}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        slab.statusColor === 'green'
                          ? 'bg-green-900 text-green-300'
                          : 'bg-red-900 text-red-300'
                      }`}
                    >
                      {slab.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-3">
                      <button className="text-blue-400 hover:text-blue-300 transition-colors p-1">
                        <Edit className="w-5 h-5" />
                      </button>
                      <button className="text-red-400 hover:text-red-300 transition-colors p-1">
                        <Trash2 className="w-5 h-5" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-300 transition-colors p-1">
                        <ToggleRight className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="px-6 py-4 border-t border-gray-700 flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Showing <span className="font-medium text-gray-300">1</span> to{" "}
            <span className="font-medium text-gray-300">{filteredSlabs.length}</span> of{" "}
            <span className="font-medium text-gray-300">{filteredSlabs.length}</span> results
          </div>
          <div className="flex space-x-2">
            <button className="px-4 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
              Previous
            </button>
            <button className="px-4 py-2 border border-blue-500 bg-blue-600 text-white rounded-lg text-sm font-medium">
              1
            </button>
            <button className="px-4 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDepositSlabs