import React, { useEffect } from "react";
import {
  <PERSON>et,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Users,
  Package,
  Plus,
  LineChart,
  Copy,
  Share,
  Award,
} from "lucide-react";

const UserOverview: React.FC = () => {
  useEffect(() => {
    const chartDom = document.getElementById("profit-chart");
    if (chartDom) {
      // Simple chart implementation without external dependencies
      const canvas = document.createElement('canvas');
      canvas.width = chartDom.offsetWidth;
      canvas.height = 256;
      canvas.style.width = '100%';
      canvas.style.height = '256px';
      chartDom.appendChild(canvas);

      const ctx = canvas.getContext('2d');
      if (ctx) {
        const data = [1.2, 1.8, 2.5, 2.1, 2.9, 3.2, 2.7];
        const labels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

        // Clear canvas
        ctx.fillStyle = '#1e293b';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Chart dimensions
        const padding = 40;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;

        // Calculate points
        const maxValue = Math.max(...data);
        const minValue = Math.min(...data);
        const range = maxValue - minValue;

        const points = data.map((value, index) => ({
          x: padding + (index * chartWidth) / (data.length - 1),
          y: padding + chartHeight - ((value - minValue) / range) * chartHeight
        }));

        // Draw grid lines
        ctx.strokeStyle = '#374151';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 4; i++) {
          const y = padding + (i * chartHeight) / 4;
          ctx.beginPath();
          ctx.moveTo(padding, y);
          ctx.lineTo(canvas.width - padding, y);
          ctx.stroke();
        }

        // Draw area under curve
        ctx.fillStyle = 'rgba(79, 70, 229, 0.2)';
        ctx.beginPath();
        ctx.moveTo(points[0].x, canvas.height - padding);
        points.forEach(point => ctx.lineTo(point.x, point.y));
        ctx.lineTo(points[points.length - 1].x, canvas.height - padding);
        ctx.closePath();
        ctx.fill();

        // Draw line
        ctx.strokeStyle = '#4F46E5';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        points.forEach(point => ctx.lineTo(point.x, point.y));
        ctx.stroke();

        // Draw points
        ctx.fillStyle = '#4F46E5';
        points.forEach(point => {
          ctx.beginPath();
          ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
          ctx.fill();
        });

        // Draw labels
        ctx.fillStyle = '#9CA3AF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        labels.forEach((label, index) => {
          const x = padding + (index * chartWidth) / (data.length - 1);
          ctx.fillText(label, x, canvas.height - 10);
        });
      }

      return () => {
        chartDom.innerHTML = '';
      };
    }
  }, []);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      const notification = document.getElementById("copy-notification");
      if (notification) {
        notification.classList.remove("opacity-0");
        setTimeout(() => {
          notification.classList.add("opacity-0");
        }, 2000);
      }
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className="min-h-screen bg-[#0D1117] text-white">
      <main className="min-h-screen">
        <div className="p-6 space-y-6">
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>

          {/* Balance Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Balance Card */}
            <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-300">
                  Total USDT Balance
                </h3>
                <span className="text-xs bg-[#131722] text-blue-400 px-2 py-1 rounded-full">
                  Main
                </span>
              </div>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-white">5,280.42</span>
                <span className="ml-1 text-sm text-gray-400">USDT</span>
              </div>
              <div className="mt-2 text-sm text-gray-400">
                ≈ ₹438,274.86 INR
              </div>
              <div className="mt-4 flex space-x-3">
                <button className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] hover:from-[#3B5FEF] hover:to-[#5C7FEF] text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                  <Plus className="w-4 h-4 mr-2" />
                  Deposit
                </button>
                <button className="bg-[#131722] text-blue-400 border border-blue-400 hover:bg-[#1A1F2E] px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center">
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Withdraw
                </button>
              </div>
            </div>

            {/* Total Profit Card */}
            <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-300">
                  Total Profit
                </h3>
                <span className="text-xs bg-green-900/30 text-green-400 px-2 py-1 rounded-full">
                  +12.5%
                </span>
              </div>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-white">658.42</span>
                <span className="ml-1 text-sm text-gray-400">USDT</span>
              </div>
              <div className="mt-2 text-sm text-gray-400">≈ ₹54,648.86 INR</div>
              <div className="mt-4">
                <div className="flex items-center text-sm text-green-400">
                  <ArrowUp className="w-4 h-4 mr-1" />
                  <span>Up 2.3% from last week</span>
                </div>
              </div>
            </div>

            {/* Today's Profit Card */}
            <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-300">
                  Today's Profit
                </h3>
                <span className="text-xs bg-green-900/30 text-green-400 px-2 py-1 rounded-full">
                  +2.7%
                </span>
              </div>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-white">+142.57</span>
                <span className="ml-1 text-sm text-gray-400">USDT</span>
              </div>
              <div className="mt-2 text-sm text-gray-400">≈ ₹11,833.31 INR</div>
              <div className="mt-4">
                <div className="flex items-center text-sm text-green-400">
                  <ArrowUp className="w-4 h-4 mr-1" />
                  <span>Up 0.5% from yesterday</span>
                </div>
              </div>
            </div>
          </div>

          {/* Profit Chart */}
          <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">Profit History</h3>
              <div className="flex space-x-2">
                <button className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white px-3 py-1 rounded-full text-sm font-medium">
                  Week
                </button>
                <button className="bg-[#131722] hover:bg-[#1A1F2E] text-gray-400 px-3 py-1 rounded-full text-sm font-medium transition-colors">
                  Month
                </button>
                <button className="bg-[#131722] hover:bg-[#1A1F2E] text-gray-400 px-3 py-1 rounded-full text-sm font-medium transition-colors">
                  Year
                </button>
              </div>
            </div>
            <div id="profit-chart" className="w-full h-64"></div>
          </div>

          {/* Quick Actions */}
          <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-6">
              Quick Actions
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex flex-col items-center p-4 bg-[#131722] hover:bg-[#1A1F2E] rounded-lg cursor-pointer transition-colors">
                <div className="w-12 h-12 bg-[#1A1F2E] rounded-full flex items-center justify-center mb-3">
                  <Wallet className="w-6 h-6 text-blue-400" />
                </div>
                <span className="text-sm font-medium text-white">Deposit</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-[#131722] hover:bg-[#1A1F2E] rounded-lg cursor-pointer transition-colors">
                <div className="w-12 h-12 bg-[#1A1F2E] rounded-full flex items-center justify-center mb-3">
                  <ArrowRight className="w-6 h-6 text-blue-400" />
                </div>
                <span className="text-sm font-medium text-white">Withdraw</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-[#131722] hover:bg-[#1A1F2E] rounded-lg cursor-pointer transition-colors">
                <div className="w-12 h-12 bg-[#1A1F2E] rounded-full flex items-center justify-center mb-3">
                  <Users className="w-6 h-6 text-blue-400" />
                </div>
                <span className="text-sm font-medium text-white">
                  Referrals
                </span>
              </div>
              <div className="flex flex-col items-center p-4 bg-[#131722] hover:bg-[#1A1F2E] rounded-lg cursor-pointer transition-colors">
                <div className="w-12 h-12 bg-[#1A1F2E] rounded-full flex items-center justify-center mb-3">
                  <Package className="w-6 h-6 text-blue-400" />
                </div>
                <span className="text-sm font-medium text-white">Products</span>
              </div>
            </div>
          </div>

          {/* Membership Status Section */}
          <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">
                Membership Status
              </h3>
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium text-yellow-500">
                  Bronze Member
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-300">
                    Referrals Progress
                  </span>
                  <span className="text-sm text-white">24/25</span>
                </div>
                <div className="w-full bg-[#131722] rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full w-[96%]"></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-300">
                    Deposit Progress
                  </span>
                  <span className="text-sm text-white">₹20,000/₹25,000</span>
                </div>
                <div className="w-full bg-[#131722] rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full w-[80%]"></div>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4 mt-6">
                {[
                  { name: 'Bronze', color: 'text-amber-600', active: true },
                  { name: 'Silver', color: 'text-gray-400', active: false },
                  { name: 'Gold', color: 'text-yellow-400', active: false },
                  { name: 'Platinum', color: 'text-gray-300', active: false }
                ].map((tier, index) => (
                  <div key={tier.name} className="text-center">
                    <div className={`w-12 h-12 mx-auto ${tier.active ? 'bg-amber-600' : 'bg-[#131722]'} bg-opacity-20 rounded-full flex items-center justify-center mb-2`}>
                      <Award className={`w-6 h-6 ${tier.color}`} />
                    </div>
                    <span className="text-xs text-gray-400">{tier.name}</span>
                  </div>
                ))}
              </div>
              <div className="mt-6 text-sm text-gray-300">
                <p>Next tier benefits:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Increased referral commission to 12%</li>
                  <li>Priority customer support</li>
                  <li>Lower trading fees</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Referral Section */}
          <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">
                Your Referral Link
              </h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-300">Total Referrals: </span>
                <span className="text-sm font-medium text-white">24</span>
              </div>
            </div>
            <div className="bg-[#131722] p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-sm text-gray-300 mb-2">
                    Share this link to earn 10% commission on referral trades
                  </div>
                  <div className="bg-[#0D1117] p-3 rounded flex items-center justify-between">
                    <span className="text-sm text-white truncate">
                      https://trading.com/ref/YOUR_UNIQUE_CODE
                    </span>
                    <button
                      className="ml-2 text-blue-400 hover:text-blue-300 transition-colors"
                      onClick={() => copyToClipboard("https://trading.com/ref/YOUR_UNIQUE_CODE")}
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] hover:from-[#3B5FEF] hover:to-[#5C7FEF] text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                  <Share className="w-4 h-4 mr-2" />
                  Share
                </button>
              </div>
              <div
                id="copy-notification"
                className="mt-2 text-sm text-green-400 opacity-0 transition-opacity duration-300"
              >
                Link copied to clipboard!
              </div>
            </div>
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-[#131722] p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-400 mb-2">
                  $1,245.80
                </div>
                <div className="text-sm text-gray-300">
                  Total Referral Earnings
                </div>
              </div>
              <div className="bg-[#131722] p-4 rounded-lg">
                <div className="text-2xl font-bold text-white mb-2">24</div>
                <div className="text-sm text-gray-300">Total Referrals</div>
              </div>
              <div className="bg-[#131722] p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-400 mb-2">
                  10%
                </div>
                <div className="text-sm text-gray-300">Commission Rate</div>
              </div>
            </div>
          </div>

          {/* Recent Transactions */}
          <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">
                Recent Transactions
              </h3>
              <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
                View All
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-[#131722]">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-[#1A1F2E] divide-y divide-gray-700">
                  {[
                    { type: 'Deposit', amount: '+1,000 USDT', status: 'Completed', date: 'Apr 29, 2025', icon: ArrowDown, iconColor: 'text-green-400', iconBg: 'bg-green-900/30', statusColor: 'bg-green-900/30 text-green-400' },
                    { type: 'Profit', amount: '+27.50 USDT', status: 'Completed', date: 'Apr 28, 2025', icon: LineChart, iconColor: 'text-blue-400', iconBg: 'bg-[#131722]', statusColor: 'bg-green-900/30 text-green-400' },
                    { type: 'Withdrawal', amount: '-500 USDT', status: 'Pending', date: 'Apr 27, 2025', icon: ArrowUp, iconColor: 'text-red-400', iconBg: 'bg-red-900/30', statusColor: 'bg-yellow-900/30 text-yellow-400' },
                    { type: 'Referral Bonus', amount: '+50 USDT', status: 'Completed', date: 'Apr 26, 2025', icon: Users, iconColor: 'text-blue-400', iconBg: 'bg-[#131722]', statusColor: 'bg-green-900/30 text-green-400' }
                  ].map((transaction, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`flex-shrink-0 h-8 w-8 ${transaction.iconBg} rounded-full flex items-center justify-center`}>
                            <transaction.icon className={`w-4 h-4 ${transaction.iconColor}`} />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-white">
                              {transaction.type}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        {transaction.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${transaction.statusColor}`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {transaction.date}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default UserOverview;