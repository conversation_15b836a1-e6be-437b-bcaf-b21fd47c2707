import { Check<PERSON><PERSON><PERSON>, Clock, Wallet, XCircle, Search, Filter, Eye, Check, X } from "lucide-react";
import React from "react";

interface Withdrawal {
  name: string;
  amount: string;
  wallet: string;
  date: string;
  refId: string;
  status: "Pending" | "Approved" | "Rejected";
  statusColor: "yellow" | "green" | "red";
  kyc: "Verified" | "Unverified";
}

const AdminWithdrawals: React.FC = () => {
  return (
    <div className="bg-slate-800 rounded-lg shadow-lg border border-slate-700">
      <div className="px-6 py-4 border-b border-slate-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">
          Withdrawal Requests
        </h3>
        <div className="flex space-x-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search withdrawals..."
              className="pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-colors duration-200 flex items-center space-x-2">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>
      </div>
      
      <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-purple-600 to-indigo-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Total Withdrawals</span>
            <Wallet className="w-5 h-5" />
          </div>
          <div className="text-2xl font-bold">USDT 425,890</div>
          <div className="text-xs mt-2">
            <span className="opacity-75">+12.5% from last month</span>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Pending Requests</span>
            <Clock className="w-5 h-5" />
          </div>
          <div className="text-2xl font-bold">23</div>
          <div className="text-xs mt-2">
            <span className="opacity-75">Updated 5 minutes ago</span>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Approved Today</span>
            <CheckCircle className="w-5 h-5" />
          </div>
          <div className="text-2xl font-bold">15</div>
          <div className="text-xs mt-2">
            <span className="opacity-75">$52,500 total amount</span>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-red-600 to-red-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Rejected Today</span>
            <XCircle className="w-5 h-5" />
          </div>
          <div className="text-2xl font-bold">3</div>
          <div className="text-xs mt-2">
            <span className="opacity-75">Due to KYC issues</span>
          </div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-700/50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                User
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Amount
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Wallet/Bank Details
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Reference ID
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {([
              {
                name: "James Wilson",
                amount: "$3,500",
                wallet: "USDT TRC20: TJkE9k...x8F2",
                date: "Apr 29, 2025 - 11:45 AM",
                refId: "WDR-********-001",
                status: "Pending" as const,
                statusColor: "yellow" as const,
                kyc: "Verified" as const,
              },
              {
                name: "Sophie Chen",
                amount: "$12,000",
                wallet: "USDT ERC20: 0x8F3c...9A2",
                date: "Apr 29, 2025 - 10:30 AM",
                refId: "WDR-********-002",
                status: "Pending" as const,
                statusColor: "yellow" as const,
                kyc: "Verified" as const,
              },
              {
                name: "Alex Thompson",
                amount: "$5,000",
                wallet: "Bank Transfer",
                date: "Apr 29, 2025 - 09:15 AM",
                refId: "WDR-********-003",
                status: "Approved" as const,
                statusColor: "green" as const,
                kyc: "Verified" as const,
              },
              {
                name: "Maria Garcia",
                amount: "$8,500",
                wallet: "USDT TRC20: TWr5Ns...p7H",
                date: "Apr 29, 2025 - 08:20 AM",
                refId: "WDR-********-004",
                status: "Rejected" as const,
                statusColor: "red" as const,
                kyc: "Unverified" as const,
              },
              {
                name: "David Kim",
                amount: "$2,300",
                wallet: "Bank Transfer",
                date: "Apr 29, 2025 - 07:45 AM",
                refId: "WDR-********-005",
                status: "Approved" as const,
                statusColor: "green" as const,
                kyc: "Verified" as const,
              },
            ] as Withdrawal[]).map((withdrawal: Withdrawal, index: number) => (
              <tr key={index} className="hover:bg-slate-700/30 transition-colors duration-150">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-blue-600/20 border border-blue-500 flex items-center justify-center">
                      <span className="text-blue-400 font-medium">
                        {withdrawal.name.charAt(0)}
                      </span>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-white">
                        {withdrawal.name}
                      </div>
                      <div className="text-sm text-slate-300">
                        KYC: <span className={withdrawal.kyc === "Verified" ? "text-green-400" : "text-red-400"}>{withdrawal.kyc}</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-white">
                    {withdrawal.amount}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300 max-w-xs truncate font-mono">
                    {withdrawal.wallet}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300">{withdrawal.date}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300 font-mono">
                    {withdrawal.refId}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      withdrawal.statusColor === 'green' ? 'bg-green-900 text-green-300' :
                      withdrawal.statusColor === 'yellow' ? 'bg-yellow-900 text-yellow-300' :
                      'bg-red-900 text-red-300'
                    }`}
                  >
                    {withdrawal.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-3">
                    <button 
                      className="text-blue-400 hover:text-blue-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                      aria-label="View withdrawal"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    {withdrawal.status === "Pending" && (
                      <>
                        <button 
                          className="text-green-400 hover:text-green-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                          aria-label="Approve withdrawal"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                        <button 
                          className="text-red-400 hover:text-red-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                          aria-label="Reject withdrawal"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="px-6 py-4 border-t border-slate-700 flex items-center justify-between">
        <div className="text-sm text-slate-300">
          Showing <span className="font-medium text-white">1</span> to{" "}
          <span className="font-medium text-white">5</span> of{" "}
          <span className="font-medium text-white">25</span> results
        </div>
        <div className="flex space-x-2">
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            Previous
          </button>
          <button className="px-3 py-1 border border-blue-500 bg-blue-600 text-white rounded-md text-sm font-medium">
            1
          </button>
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            2
          </button>
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            3
          </button>
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminWithdrawals;