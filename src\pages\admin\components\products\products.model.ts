// Admin Products Management Models and Interfaces

import { ApiResponse } from '../../../../api/axiosInstance';

// Base pagination interface
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginationResponse<T> {
  total: number;
  skip: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
  items: T[];
}

// Product interface
export interface Product {
  id: number;
  name: string;
  description: string;
  image_url: string;
  active: boolean;
  created_at: string;
}

// Create product request interface
export interface CreateProductRequest {
  name: string;
  description: string;
  image_url: string;
  active?: boolean;
}

// Update product request interface
export interface UpdateProductRequest {
  name?: string;
  description?: string;
  image_url?: string;
  active?: boolean;
}

// API Response interfaces
export interface GetProductsResponse extends ApiResponse<PaginationResponse<Product>> {}
export interface GetProductResponse extends ApiResponse<Product> {}
export interface CreateProductResponse extends ApiResponse<Product> {}
export interface UpdateProductResponse extends ApiResponse<Product> {}
export interface DeleteProductResponse extends ApiResponse<{ message: string }> {}

// Form validation errors
export interface ProductFormErrors {
  name?: string;
  description?: string;
  image_url?: string;
  active?: string;
  general?: string;
}

// Product filters for search/filtering
export interface ProductFilters extends PaginationParams {
  search?: string;
  active?: boolean;
  created_from?: string;
  created_to?: string;
}

// Product statistics interface
export interface ProductStatistics {
  total_products: number;
  active_products: number;
  inactive_products: number;
  recent_products: number;
}

// File upload for product images
export interface ProductImageUpload {
  file: File;
  preview: string;
}

// Product image upload request
export interface ProductImageUploadRequest {
  product_id: number;
  file: File;
}

// Product image upload response
export interface ProductImageUploadResponse extends ApiResponse<{
  image_url: string;
  image_id?: string;
}> {}
