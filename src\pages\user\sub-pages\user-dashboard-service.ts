// User Dashboard Service

import { apiMethods, apiUtils, tokenUtils } from '../../../api/axiosInstance';
import {
  UserDashboardOverview,
  UserWallet,
  UserActivity,
  UserDepositsSummary,
  UserWithdrawalsSummary,
  UserProfitSummary,
  UserReferralSummary,
  UserChartData,
  UserNotification,
  UserQuickStat,
  UserDashboardFilters,
  GetUserDashboardOverviewResponse,
  GetUserWalletResponse,
  GetUserActivitiesResponse,
  GetUserDepositsSummaryResponse,
  GetUserWithdrawalsSummaryResponse,
  GetUserProfitSummaryResponse,
  GetUserReferralSummaryResponse,
  GetUserChartDataResponse,
  GetUserNotificationsResponse,
  GetUserQuickStatsResponse,
  UpdateUserProfileRequest,
  UserSecuritySettings
} from './user-dashboard.model';

class UserDashboardService {
  private readonly baseUrl = '/user/dashboard';

  /**
   * Get current user ID from token
   */
  private getCurrentUserId(): number | null {
    const user = tokenUtils.getUserFromToken();
    return user?.id || user?.user_id || null;
  }

  /**
   * Get user dashboard overview
   */
  async getUserDashboardOverview(filters?: UserDashboardFilters): Promise<GetUserDashboardOverviewResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/overview?${queryString}` : `${this.baseUrl}/overview`;
      
      const response = await apiMethods.get<GetUserDashboardOverviewResponse>(url);
      
      return {
        success: true,
        message: 'Dashboard overview retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user dashboard overview error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user wallet information
   */
  async getUserWallet(): Promise<GetUserWalletResponse> {
    try {
      const userId = this.getCurrentUserId();
      if (!userId) {
        return {
          success: false,
          message: 'User not authenticated',
          error: 'Authentication required'
        };
      }

      const response = await apiMethods.get<GetUserWalletResponse>(`/wallets/${userId}`);
      
      return {
        success: true,
        message: 'Wallet information retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user wallet error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user recent activities
   */
  async getUserActivities(limit: number = 10, filters?: UserDashboardFilters): Promise<GetUserActivitiesResponse> {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);
      if (filters?.activity_type) params.append('activity_type', filters.activity_type);

      const queryString = params.toString();
      const url = `${this.baseUrl}/activities?${queryString}`;
      
      const response = await apiMethods.get<GetUserActivitiesResponse>(url);
      
      return {
        success: true,
        message: 'User activities retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user activities error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user deposits summary
   */
  async getUserDepositsSummary(filters?: UserDashboardFilters): Promise<GetUserDepositsSummaryResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/deposits-summary?${queryString}` : `${this.baseUrl}/deposits-summary`;
      
      const response = await apiMethods.get<GetUserDepositsSummaryResponse>(url);
      
      return {
        success: true,
        message: 'Deposits summary retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user deposits summary error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user withdrawals summary
   */
  async getUserWithdrawalsSummary(filters?: UserDashboardFilters): Promise<GetUserWithdrawalsSummaryResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/withdrawals-summary?${queryString}` : `${this.baseUrl}/withdrawals-summary`;
      
      const response = await apiMethods.get<GetUserWithdrawalsSummaryResponse>(url);
      
      return {
        success: true,
        message: 'Withdrawals summary retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user withdrawals summary error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user profit summary
   */
  async getUserProfitSummary(filters?: UserDashboardFilters): Promise<GetUserProfitSummaryResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/profit-summary?${queryString}` : `${this.baseUrl}/profit-summary`;
      
      const response = await apiMethods.get<GetUserProfitSummaryResponse>(url);
      
      return {
        success: true,
        message: 'Profit summary retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user profit summary error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user referral summary
   */
  async getUserReferralSummary(): Promise<GetUserReferralSummaryResponse> {
    try {
      const response = await apiMethods.get<GetUserReferralSummaryResponse>(`${this.baseUrl}/referral-summary`);
      
      return {
        success: true,
        message: 'Referral summary retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user referral summary error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user chart data
   */
  async getUserChartData(filters?: UserDashboardFilters): Promise<GetUserChartDataResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/charts?${queryString}` : `${this.baseUrl}/charts`;
      
      const response = await apiMethods.get<GetUserChartDataResponse>(url);
      
      return {
        success: true,
        message: 'Chart data retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user chart data error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user notifications
   */
  async getUserNotifications(unread_only: boolean = false): Promise<GetUserNotificationsResponse> {
    try {
      const params = new URLSearchParams();
      if (unread_only) params.append('unread_only', 'true');

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/notifications?${queryString}` : `${this.baseUrl}/notifications`;
      
      const response = await apiMethods.get<GetUserNotificationsResponse>(url);
      
      return {
        success: true,
        message: 'Notifications retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user notifications error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user quick stats
   */
  async getUserQuickStats(filters?: UserDashboardFilters): Promise<GetUserQuickStatsResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/quick-stats?${queryString}` : `${this.baseUrl}/quick-stats`;
      
      const response = await apiMethods.get<GetUserQuickStatsResponse>(url);
      
      return {
        success: true,
        message: 'Quick stats retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user quick stats error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId: number): Promise<{ success: boolean; message: string; error?: string }> {
    try {
      await apiMethods.patch(`${this.baseUrl}/notifications/${notificationId}/read`);
      
      return {
        success: true,
        message: 'Notification marked as read'
      };
    } catch (error: any) {
      console.error('Mark notification as read error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsAsRead(): Promise<{ success: boolean; message: string; error?: string }> {
    try {
      await apiMethods.patch(`${this.baseUrl}/notifications/read-all`);
      
      return {
        success: true,
        message: 'All notifications marked as read'
      };
    } catch (error: any) {
      console.error('Mark all notifications as read error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(profileData: UpdateUserProfileRequest): Promise<{ success: boolean; message: string; error?: string; data?: any }> {
    try {
      const userId = this.getCurrentUserId();
      if (!userId) {
        return {
          success: false,
          message: 'User not authenticated',
          error: 'Authentication required'
        };
      }

      const response = await apiMethods.put(`/users/${userId}`, profileData);
      
      return {
        success: true,
        message: 'Profile updated successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Update user profile error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get user security settings
   */
  async getUserSecuritySettings(): Promise<{ success: boolean; data?: UserSecuritySettings; message: string; error?: string }> {
    try {
      const response = await apiMethods.get<{ data: UserSecuritySettings }>(`${this.baseUrl}/security`);
      
      return {
        success: true,
        message: 'Security settings retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get user security settings error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Refresh dashboard data (helper method to get all dashboard data at once)
   */
  async refreshDashboardData(filters?: UserDashboardFilters): Promise<{
    overview?: UserDashboardOverview;
    wallet?: UserWallet;
    activities?: UserActivity[];
    quickStats?: UserQuickStat[];
    notifications?: UserNotification[];
  }> {
    try {
      const [overviewRes, walletRes, activitiesRes, quickStatsRes, notificationsRes] = await Promise.allSettled([
        this.getUserDashboardOverview(filters),
        this.getUserWallet(),
        this.getUserActivities(5, filters),
        this.getUserQuickStats(filters),
        this.getUserNotifications(true)
      ]);

      return {
        overview: overviewRes.status === 'fulfilled' && overviewRes.value.success ? overviewRes.value.data : undefined,
        wallet: walletRes.status === 'fulfilled' && walletRes.value.success ? walletRes.value.data : undefined,
        activities: activitiesRes.status === 'fulfilled' && activitiesRes.value.success ? activitiesRes.value.data : undefined,
        quickStats: quickStatsRes.status === 'fulfilled' && quickStatsRes.value.success ? quickStatsRes.value.data : undefined,
        notifications: notificationsRes.status === 'fulfilled' && notificationsRes.value.success ? notificationsRes.value.data : undefined,
      };
    } catch (error: any) {
      console.error('Refresh dashboard data error:', error);
      return {};
    }
  }
}

// Export singleton instance
export const userDashboardService = new UserDashboardService();
export default userDashboardService;
