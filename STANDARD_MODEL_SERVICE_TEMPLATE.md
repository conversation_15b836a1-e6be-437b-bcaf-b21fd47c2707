# Standard Model and Service Template

## Model File Template (`component-name.model.ts`)

```typescript
// [Component Name] Management Models and Interfaces

import { ApiResponse } from '../../../../api/axiosInstance';

// ==================== BASE INTERFACES ====================

// Base pagination interface
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginationResponse<T> {
  total: number;
  skip: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
  items: T[];
}

// ==================== ENUMS ====================

export type EntityStatus = 'active' | 'inactive' | 'pending' | 'suspended';
export type EntityType = 'type1' | 'type2' | 'type3';
// Add more enums as needed

// ==================== MAIN INTERFACES ====================

// Main entity interface
export interface Entity {
  id: number;
  name: string;
  description?: string;
  status: EntityStatus;
  type: EntityType;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  // Add entity-specific fields
}

// ==================== REQUEST INTERFACES ====================

// Create entity request interface
export interface CreateEntityRequest {
  name: string;
  description?: string;
  type: EntityType;
  is_active?: boolean;
  // Add required fields for creation
}

// Update entity request interface
export interface UpdateEntityRequest {
  name?: string;
  description?: string;
  status?: EntityStatus;
  type?: EntityType;
  is_active?: boolean;
  // Add optional fields for updates
}

// Entity filters for search/filtering
export interface EntityFilters extends PaginationParams {
  search?: string;
  status?: EntityStatus;
  type?: EntityType;
  is_active?: boolean;
  created_from?: string;
  created_to?: string;
  // Add filter-specific fields
}

// ==================== RESPONSE INTERFACES ====================

// API Response interfaces
export interface GetEntitiesResponse extends ApiResponse<PaginationResponse<Entity>> {}
export interface GetEntityResponse extends ApiResponse<Entity> {}
export interface CreateEntityResponse extends ApiResponse<Entity> {}
export interface UpdateEntityResponse extends ApiResponse<Entity> {}
export interface DeleteEntityResponse extends ApiResponse<{ message: string }> {}

// ==================== FORM VALIDATION ====================

// Form validation errors
export interface EntityFormErrors {
  name?: string;
  description?: string;
  status?: string;
  type?: string;
  is_active?: string;
  general?: string;
  // Add field-specific error types
}

// ==================== STATISTICS & ANALYTICS ====================

// Entity statistics interface
export interface EntityStatistics {
  total_entities: number;
  active_entities: number;
  inactive_entities: number;
  entities_by_type: Record<EntityType, number>;
  recent_entities: number;
  // Add statistics-specific fields
}
```

## Service File Template (`component-name-service.ts`)

```typescript
// [Component Name] Management Service

import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
import {
  Entity,
  CreateEntityRequest,
  UpdateEntityRequest,
  GetEntitiesResponse,
  GetEntityResponse,
  CreateEntityResponse,
  UpdateEntityResponse,
  DeleteEntityResponse,
  EntityFilters,
  EntityStatistics
} from './component-name.model';

class EntityService {
  private readonly baseUrl = '/api-endpoint';

  // ==================== CORE CRUD METHODS ====================

  /**
   * Get all entities with pagination and filters
   */
  async getEntities(filters?: EntityFilters): Promise<GetEntitiesResponse> {
    try {
      const params = new URLSearchParams();
      
      // Add pagination params
      if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
      if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
      
      // Add filter params
      if (filters?.search) params.append('search', filters.search);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
      if (filters?.created_from) params.append('created_from', filters.created_from);
      if (filters?.created_to) params.append('created_to', filters.created_to);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
      
      const response = await apiMethods.get<GetEntitiesResponse>(url);
      
      return {
        success: true,
        message: 'Entities retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get entities error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get entity by ID
   */
  async getEntityById(entityId: number): Promise<GetEntityResponse> {
    try {
      const response = await apiMethods.get<GetEntityResponse>(`${this.baseUrl}/${entityId}`);
      
      return {
        success: true,
        message: 'Entity retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get entity error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      if (errorInfo.status === 404) {
        return {
          success: false,
          message: 'Entity not found',
          error: 'Entity not found'
        };
      }
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Create new entity
   */
  async createEntity(entityData: CreateEntityRequest): Promise<CreateEntityResponse> {
    try {
      const response = await apiMethods.post<CreateEntityResponse>(this.baseUrl, entityData);
      
      return {
        success: true,
        message: 'Entity created successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Create entity error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      if (errorInfo.status === 409) {
        return {
          success: false,
          message: 'Entity already exists',
          error: 'Duplicate entity'
        };
      }
      
      if (errorInfo.status === 422) {
        return {
          success: false,
          message: 'Validation failed',
          errors: errorInfo.errors
        };
      }
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Update entity
   */
  async updateEntity(entityId: number, entityData: UpdateEntityRequest): Promise<UpdateEntityResponse> {
    try {
      const response = await apiMethods.put<UpdateEntityResponse>(`${this.baseUrl}/${entityId}`, entityData);
      
      return {
        success: true,
        message: 'Entity updated successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Update entity error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      if (errorInfo.status === 404) {
        return {
          success: false,
          message: 'Entity not found',
          error: 'Entity not found'
        };
      }
      
      if (errorInfo.status === 422) {
        return {
          success: false,
          message: 'Validation failed',
          errors: errorInfo.errors
        };
      }
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Delete entity
   */
  async deleteEntity(entityId: number): Promise<DeleteEntityResponse> {
    try {
      const response = await apiMethods.delete<DeleteEntityResponse>(`${this.baseUrl}/${entityId}`);
      
      return {
        success: true,
        message: 'Entity deleted successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Delete entity error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      if (errorInfo.status === 404) {
        return {
          success: false,
          message: 'Entity not found',
          error: 'Entity not found'
        };
      }
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Get paginated entities (helper method)
   */
  async getPaginatedEntities(page: number = 1, limit: number = 10, filters?: Omit<EntityFilters, 'skip' | 'limit'>): Promise<GetEntitiesResponse> {
    const skip = (page - 1) * limit;
    return this.getEntities({ ...filters, skip, limit });
  }

  /**
   * Search entities
   */
  async searchEntities(query: string, filters?: EntityFilters): Promise<GetEntitiesResponse> {
    return this.getEntities({ ...filters, search: query });
  }

  /**
   * Get entity statistics
   */
  async getEntityStatistics(): Promise<{ success: boolean; data?: EntityStatistics; message: string; error?: string }> {
    try {
      const response = await apiMethods.get<{ data: EntityStatistics }>(`${this.baseUrl}/statistics`);
      
      return {
        success: true,
        message: 'Statistics retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get entity statistics error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }
}

// Export singleton instance
export const entityService = new EntityService();
export default entityService;
```

## Key Standardization Points

### 1. File Structure
- Clear section headers with `// ==================== SECTION NAME ====================`
- Consistent ordering: Base Interfaces → Enums → Main Interfaces → Request Interfaces → Response Interfaces → Form Validation → Statistics

### 2. Interface Naming
- Main entity: `Entity`
- Requests: `CreateEntityRequest`, `UpdateEntityRequest`
- Responses: `GetEntityResponse`, `CreateEntityResponse`, etc.
- Filters: `EntityFilters`
- Errors: `EntityFormErrors`
- Statistics: `EntityStatistics`

### 3. Service Structure
- Class name: `EntityService`
- Base URL property: `private readonly baseUrl`
- Method sections: Core CRUD → Helper Methods
- Consistent error handling with status code checks
- Proper TypeScript return types

### 4. Error Handling
- Always use try-catch blocks
- Check for common HTTP status codes (404, 409, 422)
- Return consistent error response format
- Include meaningful error messages

### 5. Method Naming
- CRUD: `getEntities`, `getEntityById`, `createEntity`, `updateEntity`, `deleteEntity`
- Helpers: `getPaginatedEntities`, `searchEntities`, `getEntityStatistics`
- Specific actions: `approveEntity`, `rejectEntity`, etc.

### 6. TypeScript Best Practices
- Use proper generic types for API responses
- Include optional fields with `?`
- Use union types for enums
- Extend base interfaces when appropriate
- Include timestamps (`created_at`, `updated_at`)

This template ensures all model and service files follow the same structure and patterns for consistency and maintainability.
