// Admin Deposits Management Models and Interfaces

import { ApiResponse } from '../../../../api/axiosInstance';

// Base pagination interface
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginationResponse<T> {
  total: number;
  skip: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
  items: T[];
}

// Deposit status enum
export type DepositStatus = 'pending' | 'approved' | 'rejected';

// Deposit interface
export interface Deposit {
  id: number;
  user_id: number;
  admin_wallet_id: string;
  amount: number;
  transaction_id: string;
  status: DepositStatus;
  reviewed_by_admin_id: number | null;
  approved_at: string | null;
  created_at: string;
  updated_at: string;
  // Additional fields that might be included
  user?: {
    id: number;
    name: string;
    email: string;
  };
  admin_wallet?: {
    id: number;
    wallet_address: string;
    network: string;
  };
  reviewed_by?: {
    id: number;
    name: string;
    email: string;
  };
}

// Create deposit request interface
export interface CreateDepositRequest {
  admin_wallet_id: string;
  amount: number;
  transaction_id: string;
}

// Update deposit request interface (for admin approval/rejection)
export interface UpdateDepositRequest {
  status?: DepositStatus;
  reviewed_by_admin_id?: number;
  approved_at?: string;
  rejection_reason?: string;
}

// Approve deposit request interface
export interface ApproveDepositRequest {
  approved_at?: string;
  admin_notes?: string;
}

// API Response interfaces
export interface GetDepositsResponse extends ApiResponse<PaginationResponse<Deposit>> {}
export interface GetDepositResponse extends ApiResponse<Deposit> {}
export interface CreateDepositResponse extends ApiResponse<Deposit> {}
export interface UpdateDepositResponse extends ApiResponse<Deposit> {}
export interface ApproveDepositResponse extends ApiResponse<Deposit> {}
export interface DeleteDepositResponse extends ApiResponse<{ message: string }> {}

// Form validation errors
export interface DepositFormErrors {
  admin_wallet_id?: string;
  amount?: string;
  transaction_id?: string;
  status?: string;
  rejection_reason?: string;
  general?: string;
}

// Deposit filters for search/filtering
export interface DepositFilters extends PaginationParams {
  search?: string;
  status?: DepositStatus;
  user_id?: number;
  admin_wallet_id?: string;
  reviewed_by_admin_id?: number;
  amount_min?: number;
  amount_max?: number;
  created_from?: string;
  created_to?: string;
  approved_from?: string;
  approved_to?: string;
}

// Deposit statistics interface
export interface DepositStatistics {
  total_deposits: number;
  pending_deposits: number;
  approved_deposits: number;
  rejected_deposits: number;
  total_deposit_amount: number;
  approved_deposit_amount: number;
  pending_deposit_amount: number;
  average_deposit_amount: number;
  deposits_today: number;
  deposits_this_week: number;
  deposits_this_month: number;
  recent_deposits: number;
}

// Deposit summary for dashboard
export interface DepositSummary {
  total_amount: number;
  count: number;
  status: DepositStatus;
  percentage_change?: number;
}
