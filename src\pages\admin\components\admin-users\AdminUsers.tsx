import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Search, Filter } from "lucide-react";
import React, { JSX, useState } from "react";

interface User {
  name: string;
  email: string;
  date: string;
  status: string;
  statusColor: 'green' | 'yellow' | 'red';
}

type StatusColor = 'green' | 'yellow' | 'red';

function AdminUsers(): JSX.Element {
  const [showDropdown, setShowDropdown] = useState<number | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const users: User[] = [
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "Apr 23, 2025",
      status: "Pending",
      statusColor: "yellow",
    },
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "Apr 22, 2025",
      status: "Verified",
      statusColor: "green",
    },
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "Apr 21, 2025",
      status: "Pending",
      statusColor: "yellow",
    },
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "Apr 20, 2025",
      status: "Rejected",
      statusColor: "red",
    },
    {
      name: "Robert Wilson",
      email: "<EMAIL>",
      date: "Apr 19, 2025",
      status: "Verified",
      statusColor: "green",
    },
  ];

  const getStatusStyles = (statusColor: StatusColor): string => {
    switch (statusColor) {
      case 'green':
        return 'bg-green-900/20 text-green-400 border border-green-900/30';
      case 'yellow':
        return 'bg-yellow-900/20 text-yellow-400 border border-yellow-900/30';
      case 'red':
        return 'bg-red-900/20 text-red-400 border border-red-900/30';
      default:
        return 'bg-gray-700 text-gray-300 border border-gray-600';
    }
  };

  const handleDeleteClick = (user: User, index: number): void => {
    setSelectedUser(user);
    setShowDeleteDialog(true);
    setShowDropdown(null);
  };

  const handleDropdownToggle = (index: number): void => {
    setShowDropdown(showDropdown === index ? null : index);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    // Add search logic here
    console.log('Search:', e.target.value);
  };

  const handleDeleteConfirm = (): void => {
    setShowDeleteDialog(false);
    setSelectedUser(null);
    // Add delete logic here
    console.log('User deleted:', selectedUser?.name);
  };

  const handleDeleteCancel = (): void => {
    setShowDeleteDialog(false);
    setSelectedUser(null);
  };

  return (
    <div className="bg-slate-800 rounded-lg border border-slate-700">
      {/* Header */}
      <div className="px-6 py-4 border-b border-slate-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">
          User KYC Verification
        </h3>
        <div className="flex space-x-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Search users..."
              onChange={handleSearchChange}
              className="pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white placeholder-slate-400"
            />
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
          </div>
          <button 
            type="button"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-colors duration-200 flex items-center gap-2"
          >
            <Filter className="w-4 h-4" />
            Filter
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-900/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                Registration Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                KYC Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {users.map((user, index) => (
              <tr key={index} className="hover:bg-slate-700/50 transition-colors duration-150">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-slate-600 flex items-center justify-center">
                      <span className="text-slate-200 font-medium text-sm">
                        {user.name.charAt(0)}
                      </span>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-white">
                        {user.name}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300">{user.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300">{user.date}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusStyles(user.statusColor)}`}>
                    {user.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="relative">
                    <button 
                      type="button"
                      onClick={() => handleDropdownToggle(index)}
                      className="text-slate-400 hover:text-white transition-colors duration-150 p-1 rounded-md hover:bg-slate-700"
                    >
                      <MoreVertical className="w-5 h-5" />
                    </button>
                    {showDropdown === index && (
                      <div className="absolute right-0 mt-2 w-56 bg-slate-700 rounded-lg shadow-xl border border-slate-600 z-50">
                        <div className="py-1">
                          <button 
                            type="button"
                            className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
                          >
                            <Eye className="w-4 h-4" />
                            <span>View Details</span>
                          </button>
                          <button 
                            type="button"
                            className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
                          >
                            <Edit className="w-4 h-4" />
                            <span>Edit Profile</span>
                          </button>
                          <button 
                            type="button"
                            className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
                          >
                            <Key className="w-4 h-4" />
                            <span>Reset Password</span>
                          </button>
                          <button 
                            type="button"
                            className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
                          >
                            <UserCheck className="w-4 h-4" />
                            <span>Change KYC Status</span>
                          </button>
                          <div className="border-t border-slate-600 my-1"></div>
                          <button
                            type="button"
                            onClick={() => handleDeleteClick(user, index)}
                            className="w-full px-4 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-3 transition-colors duration-150"
                          >
                            <Trash2 className="w-4 h-4" />
                            <span>Delete User</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 border-t border-slate-700 flex items-center justify-between">
        <div className="text-sm text-slate-400">
          Showing <span className="font-medium text-slate-300">1</span> to{" "}
          <span className="font-medium text-slate-300">5</span> of{" "}
          <span className="font-medium text-slate-300">42</span> results
        </div>
        <div className="flex space-x-2">
          <button 
            type="button"
            className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-150"
          >
            Previous
          </button>
          <button 
            type="button"
            className="px-3 py-1 border border-blue-500 bg-blue-600/20 text-blue-400 rounded-md text-sm font-medium"
          >
            1
          </button>
          <button 
            type="button"
            className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-150"
          >
            2
          </button>
          <button 
            type="button"
            className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-150"
          >
            3
          </button>
          <button 
            type="button"
            className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-150"
          >
            Next
          </button>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-lg border border-slate-700 max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-red-900/20 flex items-center justify-center">
                  <Trash2 className="w-6 h-6 text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Delete User</h3>
                  <p className="text-sm text-slate-400">This action cannot be undone</p>
                </div>
              </div>
              <p className="text-slate-300 mb-6">
                Are you sure you want to delete <strong className="text-white">{selectedUser?.name}</strong>? 
                This will permanently remove their account and all associated data.
              </p>
              <div className="flex gap-3 justify-end">
                <button
                  type="button"
                  onClick={handleDeleteCancel}
                  className="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white border border-slate-600 rounded-lg hover:bg-slate-700 transition-colors duration-150"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDeleteConfirm}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-150"
                >
                  Delete User
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown !== null && (
        <div 
          className="fixed inset-0 z-10" 
          onClick={() => setShowDropdown(null)}
        ></div>
      )}
    </div>
  );
}

export default AdminUsers;