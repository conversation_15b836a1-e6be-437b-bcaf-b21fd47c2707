import { Bell, BellRing, ChevronDown, HelpCircle, Apple, LogOut, Settings, User } from 'lucide-react';
import React, { useState } from 'react'
import './header.scss';

function Header() {
    const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
    const [selectedCurrency, setSelectedCurrency] = useState("USD");
    const [showNotifications, setShowNotifications] = useState(false);
    const [showProfileDropdown, setShowProfileDropdown] = useState(false);
    const [notifications, setNotifications] = useState([
      {
        id: 1,
        message: "Your deposit has been confirmed",
        time: "2 hours ago",
        read: false,
      },
      {
        id: 2,
        message: "Daily profit added: +2.5%",
        time: "5 hours ago",
        read: false,
      },
      {
        id: 3,
        message: "New referral joined your network",
        time: "Yesterday",
        read: true,
      },
    ]);

    const unreadNotificationsCount = notifications.filter((n) => !n.read).length;
    const currencies = ["USD", "EUR", "GBP", "JPY", "CNY", "INR"];

    const markAllAsRead = () => {
      setNotifications(notifications.map((n) => ({ ...n, read: true })));
    };

  return (
      <header className="bg-[#1A1F2E] shadow-lg border-b border-gray-700 h-16 flex items-center justify-between px-4 lg:px-8 fixed top-0 left-0 right-0 z-50">
        {/* Logo */}
        <div className="flex items-center">
          <div className="text-white font-bold text-xl flex items-center">
            <div className="h-8 w-8 bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] rounded-full flex items-center justify-center mr-3">
              <Apple className="w-5 h-5 text-white" />
            </div>
            <span>Fruit-O-Cart</span>
          </div>
        </div>
        {/* Currency Selector */}
        <div className="hidden md:flex items-center ml-auto mr-8">
          <div className="relative">
            <button
              onClick={() => setShowCurrencyDropdown(!showCurrencyDropdown)}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg border border-gray-600 hover:border-blue-400 transition-colors cursor-pointer bg-[#131722]"
            >
              <span className="text-sm font-medium text-white">
                {selectedCurrency}
              </span>
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </button>
            {showCurrencyDropdown && (
              <div className="absolute top-full mt-1 w-24 bg-[#1A1F2E] rounded-xl shadow-lg py-1 z-20 border border-gray-700">
                {currencies.map((currency) => (
                  <button
                    key={currency}
                    onClick={() => {
                      setSelectedCurrency(currency);
                      setShowCurrencyDropdown(false);
                    }}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-[#131722] transition-colors ${selectedCurrency === currency ? "text-blue-400 bg-[#131722]" : "text-white"}`}
                  >
                    {currency}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
        {/* User Actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 text-gray-300 hover:text-blue-400 transition-colors cursor-pointer"
            >
              <Bell className="w-6 h-6" />
              {unreadNotificationsCount > 0 && (
                <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {unreadNotificationsCount}
                </span>
              )}
            </button>
            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-[#1A1F2E] rounded-xl shadow-lg py-2 z-20 border border-gray-700">
                <div className="px-4 py-2 border-b border-gray-700 flex justify-between items-center">
                  <h3 className="text-sm font-medium text-white">
                    Notifications
                  </h3>
                  <button
                    onClick={markAllAsRead}
                    className="text-xs text-blue-400 hover:text-blue-300 cursor-pointer"
                  >
                    Mark all as read
                  </button>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`px-4 py-3 hover:bg-[#131722] transition-colors ${!notification.read ? "bg-blue-900/20" : ""}`}
                    >
                      <div className="flex items-start">
                        <div
                          className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${!notification.read ? "bg-blue-900/30" : "bg-gray-700"}`}
                        >
                          <BellRing
                            className={`w-4 h-4 ${!notification.read ? "text-blue-400" : "text-gray-400"}`}
                          />
                        </div>
                        <div className="ml-3 flex-1">
                          <p
                            className={`text-sm ${!notification.read ? "font-medium text-white" : "text-gray-300"}`}
                          >
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-400 mt-1">
                            {notification.time}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="px-4 py-2 border-t border-gray-700 text-center">
                  <button className="text-sm text-blue-400 hover:text-blue-300 cursor-pointer">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>
          {/* Profile */}
          <div className="relative">
            <button
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              className="flex items-center space-x-2 cursor-pointer hover:bg-[#131722] p-2 rounded-lg transition-colors"
            >
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] flex items-center justify-center text-white text-sm font-medium">
                JD
              </div>
              <span className="hidden md:block text-sm font-medium text-white">
                John Doe
              </span>
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </button>
            {/* Profile Dropdown */}
            {showProfileDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-[#1A1F2E] rounded-xl shadow-lg py-2 z-20 border border-gray-700">
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-gray-300 hover:bg-[#131722] hover:text-white transition-colors"
                >
                  <User className="w-4 h-4 inline mr-2 text-gray-400" /> Profile
                </a>
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-gray-300 hover:bg-[#131722] hover:text-white transition-colors"
                >
                  <Settings className="w-4 h-4 inline mr-2 text-gray-400" />{" "}
                  Settings
                </a>
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-gray-300 hover:bg-[#131722] hover:text-white transition-colors"
                >
                  <HelpCircle className="w-4 h-4 inline mr-2 text-gray-400" />{" "}
                  Help Center
                </a>
                <div className="border-t border-gray-700 my-1"></div>
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 transition-colors"
                >
                  <LogOut className="w-4 h-4 inline mr-2" /> Sign out
                </a>
              </div>
            )}
          </div>
        </div>
      </header>
  )
}

export default Header