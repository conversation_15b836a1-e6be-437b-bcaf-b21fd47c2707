// Authentication Service
import { authApiMethods, tokenUtils } from '../../api/axiosInstance';
import {
  LoginRequest,
  LoginResponse,
  SignupRequest,
  SignupResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  EmailVerificationRequest,
  EmailVerificationResponse,
  SendOTPRequest,
  SendOTPResponse,
  VerifyOTPRequest,
  VerifyOTPResponse,
  DocumentUploadRequest,
  DocumentUploadResponse,
  User
} from './login-signup.model';

class AuthService {
  /**
   * <PERSON>gin user with email and password
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await authApiMethods.login({
        email: credentials.email,
        password: credentials.password
      });

      // Handle successful login
      if (response.data?.access_token) {
        // Store tokens
        tokenUtils.setTokens(
          response.data.access_token,
          response.data.refresh_token
        );

        // Return formatted response
        return {
          success: true,
          message: 'Login successful',
          data: {
            user: response.data.user || {} as User,
            tokens: response.data
          }
        };
      }

      throw new Error('Invalid response format');
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle different error types
      if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Invalid email or password',
          error: 'Authentication failed'
        };
      }

      if (error.response?.status === 422) {
        return {
          success: false,
          message: 'Validation failed',
          errors: error.response.data?.errors || {}
        };
      }

      if (error.response?.status === 429) {
        return {
          success: false,
          message: 'Too many login attempts. Please try again later.',
          error: 'Rate limit exceeded'
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Login failed. Please try again.',
        error: error.message
      };
    }
  }

  /**
   * Register new user
   */
  async signup(userData: SignupRequest): Promise<SignupResponse> {
    try {
      const response = await authApiMethods.signup({
        fullName: userData.fullName,
        email: userData.email,
        password: userData.password,
        phoneNumber: userData.phoneNumber,
        dateOfBirth: userData.dateOfBirth,
        nationality: userData.nationality,
        countryOfResidence: userData.countryOfResidence,
        currency: userData.currency,
        address: userData.address,
        referralCode: userData.referralCode,
        termsAccepted: userData.termsAccepted,
        privacyAccepted: userData.privacyAccepted
      });

      // Handle successful signup
      if (response.data?.access_token) {
        // Store tokens
        tokenUtils.setTokens(
          response.data.access_token,
          response.data.refresh_token
        );

        return {
          success: true,
          message: 'Account created successfully',
          data: {
            user: response.data.user || {} as User,
            tokens: response.data,
            requiresEmailVerification: response.data.requiresEmailVerification
          }
        };
      }

      throw new Error('Invalid response format');
    } catch (error: any) {
      console.error('Signup error:', error);

      if (error.response?.status === 409) {
        return {
          success: false,
          message: 'An account with this email already exists',
          error: 'Email already registered'
        };
      }

      if (error.response?.status === 422) {
        return {
          success: false,
          message: 'Validation failed',
          errors: error.response.data?.errors || {}
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Registration failed. Please try again.',
        error: error.message
      };
    }
  }

  /**
   * Send forgot password email
   */
  async forgotPassword(request: ForgotPasswordRequest): Promise<ForgotPasswordResponse> {
    try {
      const response = await authApiMethods.forgotPassword(request.email);

      return {
        success: true,
        message: 'Password reset instructions sent to your email',
        data: {
          message: response.data?.message || 'Reset email sent',
          resetToken: (response.data as any)?.resetToken
        }
      };
    } catch (error: any) {
      console.error('Forgot password error:', error);

      if (error.response?.status === 404) {
        return {
          success: false,
          message: 'No account found with this email address',
          error: 'Email not found'
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Failed to send reset email',
        error: error.message
      };
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(request: ResetPasswordRequest): Promise<ResetPasswordResponse> {
    try {
      const response = await authApiMethods.resetPassword(
        request.token,
        request.newPassword
      );

      return {
        success: true,
        message: 'Password reset successfully',
        data: {
          message: response.data?.message || 'Password updated'
        }
      };
    } catch (error: any) {
      console.error('Reset password error:', error);

      if (error.response?.status === 400) {
        return {
          success: false,
          message: 'Invalid or expired reset token',
          error: 'Invalid token'
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Failed to reset password',
        error: error.message
      };
    }
  }

  /**
   * Verify email with token
   */
  async verifyEmail(request: EmailVerificationRequest): Promise<EmailVerificationResponse> {
    try {
      const response = await authApiMethods.verifyEmail(request.token);

      return {
        success: true,
        message: 'Email verified successfully',
        data: {
          message: response.data?.message || 'Email verified',
          user: (response.data as any)?.user
        }
      };
    } catch (error: any) {
      console.error('Email verification error:', error);

      if (error.response?.status === 400) {
        return {
          success: false,
          message: 'Invalid or expired verification token',
          error: 'Invalid token'
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Email verification failed',
        error: error.message
      };
    }
  }

  /**
   * Send OTP for email verification or password reset
   */
  async sendOTP(request: SendOTPRequest): Promise<SendOTPResponse> {
    try {
      // This would be a custom endpoint for OTP
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send OTP');
      }

      return {
        success: true,
        message: 'OTP sent successfully',
        data: {
          message: data.message || 'OTP sent to your email',
          expiresIn: data.expiresIn || 300 // 5 minutes default
        }
      };
    } catch (error: any) {
      console.error('Send OTP error:', error);

      return {
        success: false,
        message: error.message || 'Failed to send OTP',
        error: error.message
      };
    }
  }

  /**
   * Verify OTP
   */
  async verifyOTP(request: VerifyOTPRequest): Promise<VerifyOTPResponse> {
    try {
      // This would be a custom endpoint for OTP verification
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'OTP verification failed');
      }

      return {
        success: true,
        message: 'OTP verified successfully',
        data: {
          message: data.message || 'OTP verified',
          verified: true,
          token: data.token
        }
      };
    } catch (error: any) {
      console.error('Verify OTP error:', error);

      return {
        success: false,
        message: error.message || 'Invalid OTP',
        data: {
          message: error.message || 'Invalid OTP',
          verified: false
        }
      };
    }
  }

  /**
   * Upload document for KYC
   */
  async uploadDocument(request: DocumentUploadRequest): Promise<DocumentUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', request.file);
      formData.append('documentType', request.documentType);
      formData.append('userId', request.userId);

      const response = await fetch('/api/auth/upload-document', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Document upload failed');
      }

      return {
        success: true,
        message: 'Document uploaded successfully',
        data: {
          documentId: data.documentId,
          documentUrl: data.documentUrl,
          status: data.status || 'uploaded'
        }
      };
    } catch (error: any) {
      console.error('Document upload error:', error);

      return {
        success: false,
        message: error.message || 'Failed to upload document',
        error: error.message
      };
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Clear tokens from storage
      tokenUtils.clearTokens();

      // Optionally call logout endpoint to invalidate tokens on server
      // await authApiMethods.logout();
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear tokens even if server call fails
      tokenUtils.clearTokens();
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return tokenUtils.isAuthenticated();
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return tokenUtils.getAccessToken();
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return tokenUtils.getRefreshToken();
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;