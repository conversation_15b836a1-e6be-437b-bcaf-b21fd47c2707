import { Check, Eye, X, Search, Filter } from "lucide-react";
import React from "react";

interface Deposit {
  name: string;
  amount: string;
  date: string;
  txId: string;
  status: "Pending" | "Verified" | "Rejected";
  statusColor: "yellow" | "green" | "red";
}

const AdminDeposits: React.FC = () => {
  return (
    <div className="bg-slate-800 rounded-lg shadow-lg border border-slate-700">
      <div className="px-6 py-4 border-b border-slate-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">
          Deposit Verification
        </h3>
        <div className="flex space-x-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search deposits..."
              className="pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-colors duration-200 flex items-center space-x-2">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-700/50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                User
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Amount
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Transaction ID
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {([
              {
                name: "Emma Johnson",
                amount: "$5,000",
                date: "Apr 29, 2025 - 10:23 AM",
                txId: "TXN-38492-ADFK2",
                status: "Pending" as const,
                statusColor: "yellow" as const,
              },
              {
                name: "Michael Brown",
                amount: "$2,500",
                date: "Apr 28, 2025 - 3:45 PM",
                txId: "TXN-38491-BHJL7",
                status: "Verified" as const,
                statusColor: "green" as const,
              },
              {
                name: "Sarah Davis",
                amount: "$10,000",
                date: "Apr 28, 2025 - 11:30 AM",
                txId: "TXN-38490-CDFG9",
                status: "Pending" as const,
                statusColor: "yellow" as const,
              },
              {
                name: "Robert Wilson",
                amount: "$1,000",
                date: "Apr 27, 2025 - 5:15 PM",
                txId: "TXN-38489-DERT5",
                status: "Rejected" as const,
                statusColor: "red" as const,
              },
              {
                name: "Jennifer Lee",
                amount: "$7,500",
                date: "Apr 27, 2025 - 9:20 AM",
                txId: "TXN-38488-FGTY6",
                status: "Verified" as const,
                statusColor: "green" as const,
              },
            ] as Deposit[]).map((deposit: Deposit, index: number) => (
              <tr key={index} className="hover:bg-slate-700/30 transition-colors duration-150">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-white">
                    {deposit.name}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-white">
                    {deposit.amount}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300">{deposit.date}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-slate-300 font-mono">{deposit.txId}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      deposit.statusColor === 'green' ? 'bg-green-900 text-green-300' :
                      deposit.statusColor === 'yellow' ? 'bg-yellow-900 text-yellow-300' :
                      'bg-red-900 text-red-300'
                    }`}
                  >
                    {deposit.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-3">
                    <button 
                      className="text-blue-400 hover:text-blue-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                      aria-label="View deposit"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    {deposit.status === "Pending" && (
                      <>
                        <button 
                          className="text-green-400 hover:text-green-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                          aria-label="Approve deposit"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                        <button 
                          className="text-red-400 hover:text-red-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                          aria-label="Reject deposit"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="px-6 py-4 border-t border-slate-700 flex items-center justify-between">
        <div className="text-sm text-slate-300">
          Showing <span className="font-medium text-white">1</span> to{" "}
          <span className="font-medium text-white">5</span> of{" "}
          <span className="font-medium text-white">28</span> results
        </div>
        <div className="flex space-x-2">
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            Previous
          </button>
          <button className="px-3 py-1 border border-blue-500 bg-blue-600 text-white rounded-md text-sm font-medium">
            1
          </button>
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            2
          </button>
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            3
          </button>
          <button className="px-3 py-1 border border-slate-600 rounded-md text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200">
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

export default AdminDeposits;