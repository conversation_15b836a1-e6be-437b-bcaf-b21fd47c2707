// Admin Dashboard Management Models and Interfaces

import { ApiResponse } from '../../../../api/axiosInstance';

// ==================== BASE INTERFACES ====================

// Base pagination interface
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginationResponse<T> {
  total: number;
  skip: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
  items: T[];
}

// ==================== ENUMS ====================

export type ActivityType = 'user_registration' | 'deposit' | 'withdrawal' | 'profit_distribution' | 'kyc_verification' | 'admin_action';
export type AlertType = 'info' | 'warning' | 'error' | 'success';
export type AlertPriority = 'low' | 'medium' | 'high' | 'critical';
export type SystemStatus = 'healthy' | 'warning' | 'critical';
export type MetricType = 'deposits' | 'referrals' | 'profit_earned' | 'activity';
export type ChangeDirection = 'up' | 'down' | 'neutral';
export type StatColor = 'green' | 'red' | 'blue' | 'yellow' | 'purple';
export type StatFormat = 'number' | 'currency' | 'percentage';
export type DateRange = 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
export type UserType = 'all' | 'active' | 'inactive' | 'new';
export type WidgetSize = 'small' | 'medium' | 'large';

// ==================== MAIN INTERFACES ====================

// Dashboard overview statistics
export interface DashboardOverview {
  id?: number;
  // User metrics
  total_users: number;
  active_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;

  // Deposit metrics
  total_deposits: number;
  pending_deposits: number;
  approved_deposits: number;
  total_deposit_amount: number;
  deposits_today: number;
  deposits_this_week: number;
  deposits_this_month: number;

  // Withdrawal metrics
  total_withdrawals: number;
  pending_withdrawals: number;
  approved_withdrawals: number;
  total_withdrawal_amount: number;
  withdrawals_today: number;
  withdrawals_this_week: number;
  withdrawals_this_month: number;

  // Profit metrics
  total_profit_distributed: number;
  profit_distributed_today: number;
  profit_distributed_this_week: number;
  profit_distributed_this_month: number;

  // Referral metrics
  total_referral_bonus: number;
  referral_bonus_today: number;
  referral_bonus_this_week: number;
  referral_bonus_this_month: number;

  // Financial metrics
  platform_balance: number;
  total_revenue: number;
  net_profit: number;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

// Recent activities
export interface RecentActivity {
  id: number;
  type: ActivityType;
  title: string;
  description: string;
  user_id?: number;
  user_name?: string;
  amount?: number;
  status?: string;
  created_at: string;
  updated_at?: string;
  metadata?: Record<string, any>;
}

// Chart data interfaces
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
  metadata?: Record<string, any>;
}

export interface DashboardChartData {
  user_registrations: ChartDataPoint[];
  deposits: ChartDataPoint[];
  withdrawals: ChartDataPoint[];
  profit_distributions: ChartDataPoint[];
  revenue: ChartDataPoint[];
}

// System health metrics
export interface SystemHealth {
  id?: number;
  api_status: SystemStatus;
  database_status: SystemStatus;
  cache_status: SystemStatus;
  queue_status: SystemStatus;
  storage_status: SystemStatus;

  response_time: number;
  uptime_percentage: number;
  error_rate: number;
  active_sessions: number;

  last_backup: string;
  disk_usage_percentage: number;
  memory_usage_percentage: number;
  cpu_usage_percentage: number;

  created_at?: string;
  updated_at?: string;
}

// Top performers
export interface TopPerformer {
  id: number;
  name: string;
  email: string;
  metric_value: number;
  metric_type: MetricType;
  rank: number;
  change_from_last_period?: number;
  created_at?: string;
  updated_at?: string;
}

// Financial summary
export interface FinancialSummary {
  id?: number;
  total_deposits: number;
  total_withdrawals: number;
  total_profit_paid: number;
  total_referral_bonus_paid: number;
  platform_fees_collected: number;
  net_revenue: number;

  monthly_recurring_revenue: number;
  average_deposit_size: number;
  average_withdrawal_size: number;
  user_lifetime_value: number;

  pending_payouts: number;
  available_balance: number;
  reserved_funds: number;

  created_at?: string;
  updated_at?: string;
}

// Alerts and notifications
export interface DashboardAlert {
  id: number;
  type: AlertType;
  title: string;
  message: string;
  action_required: boolean;
  action_url?: string;
  action_text?: string;
  created_at: string;
  updated_at?: string;
  is_read: boolean;
  priority: AlertPriority;
}

// Quick stats for cards
export interface QuickStat {
  id?: number;
  title: string;
  value: number | string;
  change_percentage?: number;
  change_direction?: ChangeDirection;
  icon?: string;
  color?: StatColor;
  format?: StatFormat;
  created_at?: string;
  updated_at?: string;
}

// ==================== REQUEST INTERFACES ====================

// Dashboard filters
export interface DashboardFilters extends PaginationParams {
  date_range?: DateRange;
  start_date?: string;
  end_date?: string;
  user_type?: UserType;
  currency?: string;
  region?: string;
}

// Dashboard configuration request
export interface UpdateDashboardConfigRequest {
  refresh_interval?: number;
  default_date_range?: string;
  charts_enabled?: boolean;
  alerts_enabled?: boolean;
  real_time_updates?: boolean;
  widgets?: Array<{
    id: string;
    name: string;
    enabled: boolean;
    position: number;
    size: WidgetSize;
  }>;
}

// ==================== RESPONSE INTERFACES ====================

// API Response interfaces
export interface GetDashboardOverviewResponse extends ApiResponse<DashboardOverview> {}
export interface GetRecentActivitiesResponse extends ApiResponse<RecentActivity[]> {}
export interface GetDashboardChartDataResponse extends ApiResponse<DashboardChartData> {}
export interface GetSystemHealthResponse extends ApiResponse<SystemHealth> {}
export interface GetTopPerformersResponse extends ApiResponse<TopPerformer[]> {}
export interface GetFinancialSummaryResponse extends ApiResponse<FinancialSummary> {}
export interface GetDashboardAlertsResponse extends ApiResponse<DashboardAlert[]> {}
export interface GetQuickStatsResponse extends ApiResponse<QuickStat[]> {}
export interface GetDashboardConfigResponse extends ApiResponse<DashboardConfig> {}
export interface UpdateDashboardConfigResponse extends ApiResponse<DashboardConfig> {}
export interface MarkAlertAsReadResponse extends ApiResponse<{ message: string }> {}
export interface ExportDashboardDataResponse extends ApiResponse<Blob> {}

// ==================== FORM VALIDATION ====================

// Form validation errors
export interface DashboardFormErrors {
  date_range?: string;
  start_date?: string;
  end_date?: string;
  user_type?: string;
  currency?: string;
  region?: string;
  general?: string;
}

export interface DashboardConfigFormErrors {
  refresh_interval?: string;
  default_date_range?: string;
  charts_enabled?: string;
  alerts_enabled?: string;
  real_time_updates?: string;
  widgets?: string;
  general?: string;
}

// ==================== STATISTICS & ANALYTICS ====================

// Dashboard statistics interface
export interface DashboardStatistics {
  total_overview_requests: number;
  total_chart_requests: number;
  total_alert_requests: number;
  average_response_time: number;
  most_viewed_widgets: Array<{
    widget_id: string;
    widget_name: string;
    view_count: number;
  }>;
  user_activity_patterns: Array<{
    hour: number;
    activity_count: number;
  }>;
  recent_dashboard_updates: number;
}

// Dashboard configuration
export interface DashboardConfig {
  id?: number;
  refresh_interval: number;
  default_date_range: string;
  charts_enabled: boolean;
  alerts_enabled: boolean;
  real_time_updates: boolean;
  widgets: Array<{
    id: string;
    name: string;
    enabled: boolean;
    position: number;
    size: WidgetSize;
  }>;
  created_at?: string;
  updated_at?: string;
}
