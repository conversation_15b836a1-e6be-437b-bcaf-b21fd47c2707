// Admin Dashboard Models and Interfaces

import { ApiResponse } from '../../../../api/axiosInstance';

// Dashboard overview statistics
export interface DashboardOverview {
  total_users: number;
  active_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
  
  total_deposits: number;
  pending_deposits: number;
  approved_deposits: number;
  total_deposit_amount: number;
  deposits_today: number;
  deposits_this_week: number;
  deposits_this_month: number;
  
  total_withdrawals: number;
  pending_withdrawals: number;
  approved_withdrawals: number;
  total_withdrawal_amount: number;
  withdrawals_today: number;
  withdrawals_this_week: number;
  withdrawals_this_month: number;
  
  total_profit_distributed: number;
  profit_distributed_today: number;
  profit_distributed_this_week: number;
  profit_distributed_this_month: number;
  
  total_referral_bonus: number;
  referral_bonus_today: number;
  referral_bonus_this_week: number;
  referral_bonus_this_month: number;
  
  platform_balance: number;
  total_revenue: number;
  net_profit: number;
}

// Recent activities
export interface RecentActivity {
  id: number;
  type: 'user_registration' | 'deposit' | 'withdrawal' | 'profit_distribution' | 'kyc_verification' | 'admin_action';
  title: string;
  description: string;
  user_id?: number;
  user_name?: string;
  amount?: number;
  status?: string;
  created_at: string;
  metadata?: Record<string, any>;
}

// Chart data interfaces
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface DashboardChartData {
  user_registrations: ChartDataPoint[];
  deposits: ChartDataPoint[];
  withdrawals: ChartDataPoint[];
  profit_distributions: ChartDataPoint[];
  revenue: ChartDataPoint[];
}

// System health metrics
export interface SystemHealth {
  api_status: 'healthy' | 'warning' | 'critical';
  database_status: 'healthy' | 'warning' | 'critical';
  cache_status: 'healthy' | 'warning' | 'critical';
  queue_status: 'healthy' | 'warning' | 'critical';
  storage_status: 'healthy' | 'warning' | 'critical';
  
  response_time: number;
  uptime_percentage: number;
  error_rate: number;
  active_sessions: number;
  
  last_backup: string;
  disk_usage_percentage: number;
  memory_usage_percentage: number;
  cpu_usage_percentage: number;
}

// Top performers
export interface TopPerformer {
  id: number;
  name: string;
  email: string;
  metric_value: number;
  metric_type: 'deposits' | 'referrals' | 'profit_earned' | 'activity';
  rank: number;
  change_from_last_period?: number;
}

// Financial summary
export interface FinancialSummary {
  total_deposits: number;
  total_withdrawals: number;
  total_profit_paid: number;
  total_referral_bonus_paid: number;
  platform_fees_collected: number;
  net_revenue: number;
  
  monthly_recurring_revenue: number;
  average_deposit_size: number;
  average_withdrawal_size: number;
  user_lifetime_value: number;
  
  pending_payouts: number;
  available_balance: number;
  reserved_funds: number;
}

// Alerts and notifications
export interface DashboardAlert {
  id: number;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  action_required: boolean;
  action_url?: string;
  action_text?: string;
  created_at: string;
  is_read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// Quick stats for cards
export interface QuickStat {
  title: string;
  value: number | string;
  change_percentage?: number;
  change_direction?: 'up' | 'down' | 'neutral';
  icon?: string;
  color?: 'green' | 'red' | 'blue' | 'yellow' | 'purple';
  format?: 'number' | 'currency' | 'percentage';
}

// Dashboard filters
export interface DashboardFilters {
  date_range: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  start_date?: string;
  end_date?: string;
  user_type?: 'all' | 'active' | 'inactive' | 'new';
  currency?: string;
  region?: string;
}

// API Response interfaces
export interface GetDashboardOverviewResponse extends ApiResponse<DashboardOverview> {}
export interface GetRecentActivitiesResponse extends ApiResponse<RecentActivity[]> {}
export interface GetDashboardChartDataResponse extends ApiResponse<DashboardChartData> {}
export interface GetSystemHealthResponse extends ApiResponse<SystemHealth> {}
export interface GetTopPerformersResponse extends ApiResponse<TopPerformer[]> {}
export interface GetFinancialSummaryResponse extends ApiResponse<FinancialSummary> {}
export interface GetDashboardAlertsResponse extends ApiResponse<DashboardAlert[]> {}
export interface GetQuickStatsResponse extends ApiResponse<QuickStat[]> {}

// Dashboard configuration
export interface DashboardConfig {
  refresh_interval: number;
  default_date_range: string;
  charts_enabled: boolean;
  alerts_enabled: boolean;
  real_time_updates: boolean;
  widgets: Array<{
    id: string;
    name: string;
    enabled: boolean;
    position: number;
    size: 'small' | 'medium' | 'large';
  }>;
}
