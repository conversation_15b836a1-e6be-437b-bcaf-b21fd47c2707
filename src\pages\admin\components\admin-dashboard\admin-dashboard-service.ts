// Admin Dashboard Service

import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
import {
  DashboardOverview,
  RecentActivity,
  DashboardChartData,
  SystemHealth,
  TopPerformer,
  FinancialSummary,
  DashboardAlert,
  QuickStat,
  DashboardFilters,
  GetDashboardOverviewResponse,
  GetRecentActivitiesResponse,
  GetDashboardChartDataResponse,
  GetSystemHealthResponse,
  GetTopPerformersResponse,
  GetFinancialSummaryResponse,
  GetDashboardAlertsResponse,
  GetQuickStatsResponse,
  DashboardConfig
} from './admin-dashboard.model';

class AdminDashboardService {
  private readonly baseUrl = '/admin/dashboard';

  /**
   * Get dashboard overview statistics
   */
  async getDashboardOverview(filters?: DashboardFilters): Promise<GetDashboardOverviewResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);
      if (filters?.user_type) params.append('user_type', filters.user_type);
      if (filters?.currency) params.append('currency', filters.currency);
      if (filters?.region) params.append('region', filters.region);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/overview?${queryString}` : `${this.baseUrl}/overview`;
      
      const response = await apiMethods.get<GetDashboardOverviewResponse>(url);
      
      return {
        success: true,
        message: 'Dashboard overview retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get dashboard overview error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get recent activities
   */
  async getRecentActivities(limit: number = 10, filters?: DashboardFilters): Promise<GetRecentActivitiesResponse> {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = `${this.baseUrl}/activities?${queryString}`;
      
      const response = await apiMethods.get<GetRecentActivitiesResponse>(url);
      
      return {
        success: true,
        message: 'Recent activities retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get recent activities error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get dashboard chart data
   */
  async getDashboardChartData(filters?: DashboardFilters): Promise<GetDashboardChartDataResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);
      if (filters?.currency) params.append('currency', filters.currency);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/charts?${queryString}` : `${this.baseUrl}/charts`;
      
      const response = await apiMethods.get<GetDashboardChartDataResponse>(url);
      
      return {
        success: true,
        message: 'Dashboard chart data retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get dashboard chart data error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth(): Promise<GetSystemHealthResponse> {
    try {
      const response = await apiMethods.get<GetSystemHealthResponse>(`${this.baseUrl}/health`);
      
      return {
        success: true,
        message: 'System health retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get system health error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get top performers
   */
  async getTopPerformers(metric: 'deposits' | 'referrals' | 'profit_earned' | 'activity' = 'deposits', limit: number = 10): Promise<GetTopPerformersResponse> {
    try {
      const params = new URLSearchParams();
      params.append('metric', metric);
      params.append('limit', limit.toString());

      const url = `${this.baseUrl}/top-performers?${params.toString()}`;
      
      const response = await apiMethods.get<GetTopPerformersResponse>(url);
      
      return {
        success: true,
        message: 'Top performers retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get top performers error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get financial summary
   */
  async getFinancialSummary(filters?: DashboardFilters): Promise<GetFinancialSummaryResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);
      if (filters?.currency) params.append('currency', filters.currency);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/financial?${queryString}` : `${this.baseUrl}/financial`;
      
      const response = await apiMethods.get<GetFinancialSummaryResponse>(url);
      
      return {
        success: true,
        message: 'Financial summary retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get financial summary error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get dashboard alerts
   */
  async getDashboardAlerts(unread_only: boolean = false): Promise<GetDashboardAlertsResponse> {
    try {
      const params = new URLSearchParams();
      if (unread_only) params.append('unread_only', 'true');

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/alerts?${queryString}` : `${this.baseUrl}/alerts`;
      
      const response = await apiMethods.get<GetDashboardAlertsResponse>(url);
      
      return {
        success: true,
        message: 'Dashboard alerts retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get dashboard alerts error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get quick stats
   */
  async getQuickStats(filters?: DashboardFilters): Promise<GetQuickStatsResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/quick-stats?${queryString}` : `${this.baseUrl}/quick-stats`;
      
      const response = await apiMethods.get<GetQuickStatsResponse>(url);
      
      return {
        success: true,
        message: 'Quick stats retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get quick stats error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Mark alert as read
   */
  async markAlertAsRead(alertId: number): Promise<{ success: boolean; message: string; error?: string }> {
    try {
      await apiMethods.patch(`${this.baseUrl}/alerts/${alertId}/read`);
      
      return {
        success: true,
        message: 'Alert marked as read'
      };
    } catch (error: any) {
      console.error('Mark alert as read error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Mark all alerts as read
   */
  async markAllAlertsAsRead(): Promise<{ success: boolean; message: string; error?: string }> {
    try {
      await apiMethods.patch(`${this.baseUrl}/alerts/read-all`);
      
      return {
        success: true,
        message: 'All alerts marked as read'
      };
    } catch (error: any) {
      console.error('Mark all alerts as read error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Get dashboard configuration
   */
  async getDashboardConfig(): Promise<{ success: boolean; data?: DashboardConfig; message: string; error?: string }> {
    try {
      const response = await apiMethods.get<{ data: DashboardConfig }>(`${this.baseUrl}/config`);
      
      return {
        success: true,
        message: 'Dashboard configuration retrieved successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Get dashboard config error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Update dashboard configuration
   */
  async updateDashboardConfig(config: Partial<DashboardConfig>): Promise<{ success: boolean; data?: DashboardConfig; message: string; error?: string }> {
    try {
      const response = await apiMethods.put<{ data: DashboardConfig }>(`${this.baseUrl}/config`, config);
      
      return {
        success: true,
        message: 'Dashboard configuration updated successfully',
        data: response.data.data
      };
    } catch (error: any) {
      console.error('Update dashboard config error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }

  /**
   * Export dashboard data
   */
  async exportDashboardData(format: 'csv' | 'excel' | 'pdf' = 'csv', filters?: DashboardFilters): Promise<{ success: boolean; data?: Blob; message: string; error?: string }> {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      
      if (filters?.date_range) params.append('date_range', filters.date_range);
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);

      const queryString = params.toString();
      const url = `${this.baseUrl}/export?${queryString}`;
      
      const response = await apiMethods.get(url, { responseType: 'blob' });
      
      return {
        success: true,
        message: 'Dashboard data exported successfully',
        data: response.data
      };
    } catch (error: any) {
      console.error('Export dashboard data error:', error);
      const errorInfo = apiUtils.handleApiError(error);
      
      return {
        success: false,
        message: errorInfo.message,
        error: errorInfo.message
      };
    }
  }
}

// Export singleton instance
export const adminDashboardService = new AdminDashboardService();
export default adminDashboardService;
