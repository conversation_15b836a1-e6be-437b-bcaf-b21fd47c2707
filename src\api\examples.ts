// ==================== AXIOS INSTANCE USAGE EXAMPLES ====================

import { 
  apiMethods, 
  authApiMethods, 
  tokenUtils, 
  fileUtils, 
  apiUtils,
  ApiResponse,
  TokenResponse,
  RequestConfig,
  FileUploadConfig 
} from './axiosInstance';

// ==================== AUTHENTICATION EXAMPLES ====================

// Login example
export const loginExample = async (email: string, password: string) => {
  try {
    const response = await authApiMethods.login({ email, password });
    
    if (response.data?.access_token) {
      console.log('Login successful:', response.data);
      return { success: true, data: response.data };
    }
    
    return { success: false, message: 'Login failed' };
  } catch (error) {
    const errorInfo = apiUtils.handleApiError(error);
    console.error('Login error:', errorInfo);
    return { success: false, message: errorInfo.message };
  }
};

// Signup example
export const signupExample = async (userData: any) => {
  try {
    const response = await authApiMethods.signup(userData);
    
    if (response.data?.access_token) {
      console.log('Signup successful:', response.data);
      return { success: true, data: response.data };
    }
    
    return { success: false, message: 'Signup failed' };
  } catch (error) {
    const errorInfo = apiUtils.handleApiError(error);
    console.error('Signup error:', errorInfo);
    return { success: false, message: errorInfo.message };
  }
};

// ==================== CRUD OPERATIONS EXAMPLES ====================

// GET request example
export const getUsersExample = async () => {
  try {
    const response = await apiMethods.get<ApiResponse<any[]>>('/users');
    return response.data;
  } catch (error) {
    console.error('Get users error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// POST request example
export const createUserExample = async (userData: any) => {
  try {
    const response = await apiMethods.post<ApiResponse<any>>('/users', userData);
    return response.data;
  } catch (error) {
    console.error('Create user error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// PUT request example
export const updateUserExample = async (userId: string, userData: any) => {
  try {
    const response = await apiMethods.put<ApiResponse<any>>(`/users/${userId}`, userData);
    return response.data;
  } catch (error) {
    console.error('Update user error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// DELETE request example
export const deleteUserExample = async (userId: string) => {
  try {
    const response = await apiMethods.delete<ApiResponse<any>>(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Delete user error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// ==================== FILE UPLOAD EXAMPLES ====================

// Single file upload example
export const uploadFileExample = async (file: File) => {
  try {
    const config: FileUploadConfig = {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`Upload progress: ${percentCompleted}%`);
      }
    };

    const response = await apiMethods.uploadFile<ApiResponse<any>>('/upload', file, config);
    return response.data;
  } catch (error) {
    console.error('File upload error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// Multiple files upload example
export const uploadMultipleFilesExample = async (files: File[]) => {
  try {
    const config: FileUploadConfig = {
      maxFileSize: 10 * 1024 * 1024, // 10MB per file
      allowedTypes: ['image/*', 'application/pdf'],
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`Upload progress: ${percentCompleted}%`);
      }
    };

    const response = await apiMethods.uploadMultipleFiles<ApiResponse<any>>('/upload-multiple', files, config);
    return response.data;
  } catch (error) {
    console.error('Multiple files upload error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// File upload with additional data example
export const uploadWithDataExample = async (file: File, additionalData: any) => {
  try {
    const config: FileUploadConfig = {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/png'],
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`Upload progress: ${percentCompleted}%`);
      }
    };

    const response = await apiMethods.uploadWithData<ApiResponse<any>>(
      '/upload-with-data', 
      file, 
      additionalData, 
      config
    );
    return response.data;
  } catch (error) {
    console.error('Upload with data error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// ==================== PAGINATION EXAMPLES ====================

// Paginated data example
export const getPaginatedUsersExample = async (page: number = 1, limit: number = 10) => {
  try {
    const response = await apiMethods.getPaginated<any>('/users', page, limit);
    return response.data;
  } catch (error) {
    console.error('Get paginated users error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// Search example
export const searchUsersExample = async (query: string, filters?: any) => {
  try {
    const response = await apiMethods.search<any>('/users/search', query, filters);
    return response.data;
  } catch (error) {
    console.error('Search users error:', apiUtils.handleApiError(error));
    throw error;
  }
};

// ==================== UTILITY EXAMPLES ====================

// Token utilities example
export const tokenUtilsExample = () => {
  // Check if user is authenticated
  const isAuthenticated = tokenUtils.isAuthenticated();
  console.log('Is authenticated:', isAuthenticated);

  // Get user data from token
  const userData = tokenUtils.getUserFromToken();
  console.log('User data from token:', userData);

  // Check if token is expiring soon
  const isExpiring = tokenUtils.isTokenExpiringSoon();
  console.log('Token expiring soon:', isExpiring);

  // Get tokens
  const accessToken = tokenUtils.getAccessToken();
  const refreshToken = tokenUtils.getRefreshToken();
  console.log('Access token:', accessToken);
  console.log('Refresh token:', refreshToken);
};

// File utilities example
export const fileUtilsExample = async (file: File) => {
  // Validate file
  const validation = fileUtils.validateFile(file, {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png']
  });
  console.log('File validation:', validation);

  // Convert to base64
  try {
    const base64 = await fileUtils.fileToBase64(file);
    console.log('File as base64:', base64.substring(0, 100) + '...');
  } catch (error) {
    console.error('Base64 conversion error:', error);
  }

  // Format file size
  const formattedSize = fileUtils.formatFileSize(file.size);
  console.log('Formatted file size:', formattedSize);

  // Get file extension
  const extension = fileUtils.getFileExtension(file.name);
  console.log('File extension:', extension);
};

// ==================== ERROR HANDLING EXAMPLES ====================

// Retry mechanism example
export const retryExample = async () => {
  try {
    const result = await apiUtils.retry(
      () => apiMethods.get('/unreliable-endpoint'),
      3, // retry 3 times
      1000 // start with 1 second delay
    );
    return result.data;
  } catch (error) {
    console.error('All retry attempts failed:', apiUtils.handleApiError(error));
    throw error;
  }
};

// No-auth request example
export const noAuthExample = async () => {
  try {
    const response = await apiMethods.withoutAuth.get<ApiResponse<any>>('/public-data');
    return response.data;
  } catch (error) {
    console.error('No-auth request error:', apiUtils.handleApiError(error));
    throw error;
  }
};
