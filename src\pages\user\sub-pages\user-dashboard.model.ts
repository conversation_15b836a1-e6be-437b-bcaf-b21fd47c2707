// User Dashboard Models and Interfaces

import { ApiResponse } from '../../../api/axiosInstance';

// User dashboard overview
export interface UserDashboardOverview {
  user_id: number;
  wallet_balance: number;
  total_deposits: number;
  total_withdrawals: number;
  total_profit_earned: number;
  total_referral_bonus: number;
  active_deposits: number;
  pending_withdrawals: number;
  
  // Recent activity counts
  deposits_this_month: number;
  withdrawals_this_month: number;
  profit_this_month: number;
  referral_bonus_this_month: number;
  
  // Account status
  account_status: 'active' | 'inactive' | 'suspended';
  kyc_status: 'pending' | 'verified' | 'rejected';
  rank: 'bronze' | 'silver' | 'gold' | 'platinum';
  
  // Referral information
  referral_code: string;
  total_referrals: number;
  active_referrals: number;
  referral_levels: number;
}

// User wallet information
export interface UserWallet {
  user_id: number;
  balance: number;
  total_profit: number;
  total_referral_bonus: number;
  available_balance: number;
  locked_balance: number;
  last_updated: string;
}

// User recent activities
export interface UserActivity {
  id: number;
  type: 'deposit' | 'withdrawal' | 'profit' | 'referral_bonus' | 'transfer';
  title: string;
  description: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  reference_id?: string;
  metadata?: Record<string, any>;
}

// User deposits summary
export interface UserDepositsSummary {
  total_deposits: number;
  active_deposits: number;
  completed_deposits: number;
  total_amount: number;
  active_amount: number;
  average_deposit: number;
  last_deposit_date: string | null;
  next_profit_date: string | null;
}

// User withdrawals summary
export interface UserWithdrawalsSummary {
  total_withdrawals: number;
  pending_withdrawals: number;
  completed_withdrawals: number;
  total_amount: number;
  pending_amount: number;
  last_withdrawal_date: string | null;
  available_for_withdrawal: number;
}

// User profit summary
export interface UserProfitSummary {
  total_profit: number;
  profit_this_month: number;
  profit_this_week: number;
  profit_today: number;
  average_daily_profit: number;
  profit_rate: number;
  next_profit_date: string | null;
  profit_frequency: 'daily' | 'weekly' | 'monthly';
}

// User referral summary
export interface UserReferralSummary {
  referral_code: string;
  total_referrals: number;
  active_referrals: number;
  total_referral_bonus: number;
  referral_bonus_this_month: number;
  referral_levels: Array<{
    level: number;
    count: number;
    bonus_earned: number;
    percentage: number;
  }>;
  recent_referrals: Array<{
    id: number;
    name: string;
    email: string;
    joined_date: string;
    status: 'active' | 'inactive';
    total_deposits: number;
  }>;
}

// User chart data
export interface UserChartData {
  profit_history: Array<{
    date: string;
    amount: number;
  }>;
  deposit_history: Array<{
    date: string;
    amount: number;
  }>;
  withdrawal_history: Array<{
    date: string;
    amount: number;
  }>;
  balance_history: Array<{
    date: string;
    balance: number;
  }>;
}

// User notifications
export interface UserNotification {
  id: number;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  is_read: boolean;
  created_at: string;
  action_url?: string;
  action_text?: string;
}

// User quick stats
export interface UserQuickStat {
  title: string;
  value: number | string;
  change_percentage?: number;
  change_direction?: 'up' | 'down' | 'neutral';
  icon?: string;
  color?: 'green' | 'red' | 'blue' | 'yellow' | 'purple';
  format?: 'number' | 'currency' | 'percentage';
}

// User dashboard filters
export interface UserDashboardFilters {
  date_range: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  start_date?: string;
  end_date?: string;
  activity_type?: 'all' | 'deposit' | 'withdrawal' | 'profit' | 'referral';
}

// API Response interfaces
export interface GetUserDashboardOverviewResponse extends ApiResponse<UserDashboardOverview> {}
export interface GetUserWalletResponse extends ApiResponse<UserWallet> {}
export interface GetUserActivitiesResponse extends ApiResponse<UserActivity[]> {}
export interface GetUserDepositsSummaryResponse extends ApiResponse<UserDepositsSummary> {}
export interface GetUserWithdrawalsSummaryResponse extends ApiResponse<UserWithdrawalsSummary> {}
export interface GetUserProfitSummaryResponse extends ApiResponse<UserProfitSummary> {}
export interface GetUserReferralSummaryResponse extends ApiResponse<UserReferralSummary> {}
export interface GetUserChartDataResponse extends ApiResponse<UserChartData> {}
export interface GetUserNotificationsResponse extends ApiResponse<UserNotification[]> {}
export interface GetUserQuickStatsResponse extends ApiResponse<UserQuickStat[]> {}

// User profile update interface
export interface UpdateUserProfileRequest {
  name?: string;
  phone?: string;
  address?: string;
  preferred_currency?: string;
  notification_preferences?: {
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
    marketing_emails: boolean;
  };
}

// User security settings
export interface UserSecuritySettings {
  two_factor_enabled: boolean;
  login_notifications: boolean;
  withdrawal_confirmations: boolean;
  last_password_change: string;
  active_sessions: number;
  login_history: Array<{
    ip_address: string;
    location: string;
    device: string;
    login_time: string;
    status: 'success' | 'failed';
  }>;
}
