import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users, Search, Filter } from 'lucide-react'
import React, { useState } from 'react'

interface ProfitEntry {
  date: string
  rate: string
  amount: string
  recipients: string
  status: string
  statusColor: 'green' | 'yellow' | 'red'
}

const AdminProfit: React.FC = () => {
  const [profitRate, setProfitRate] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')

  const profitEntries: ProfitEntry[] = [
    {
      date: "Apr 29, 2025 - 10:00 AM",
      rate: "2.4%",
      amount: "$60,000",
      recipients: "1,248",
      status: "Completed",
      statusColor: "green",
    },
    {
      date: "Apr 28, 2025 - 10:00 AM",
      rate: "2.2%",
      amount: "$55,000",
      recipients: "1,235",
      status: "Completed",
      statusColor: "green",
    },
    {
      date: "Apr 27, 2025 - 10:00 AM",
      rate: "2.5%",
      amount: "$62,500",
      recipients: "1,220",
      status: "Completed",
      statusColor: "green",
    },
    {
      date: "Apr 26, 2025 - 10:00 AM",
      rate: "2.3%",
      amount: "$57,500",
      recipients: "1,210",
      status: "Completed",
      statusColor: "green",
    },
    {
      date: "Apr 25, 2025 - 10:00 AM",
      rate: "2.1%",
      amount: "$52,500",
      recipients: "1,200",
      status: "Completed",
      statusColor: "green",
    },
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle profit rate submission
    console.log('Setting profit rate:', profitRate)
  }

  const getStatusBadgeClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-emerald-900/50 text-emerald-300 border border-emerald-800'
      case 'yellow':
        return 'bg-yellow-900/50 text-yellow-300 border border-yellow-800'
      case 'red':
        return 'bg-red-900/50 text-red-300 border border-red-800'
      default:
        return 'bg-gray-700/50 text-gray-300 border border-gray-600'
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Today's Profit Rate
              </p>
              <h3 className="text-2xl font-bold text-white">2.4%</h3>
            </div>
            <div className="bg-emerald-900/30 p-3 rounded-xl border border-emerald-800/50">
              <LineChart className="text-emerald-400 w-5 h-5" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Total Users Receiving Profit
              </p>
              <h3 className="text-2xl font-bold text-white">1,248</h3>
            </div>
            <div className="bg-blue-900/30 p-3 rounded-xl border border-blue-800/50">
              <Users className="text-blue-400 w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Total Net Deposits
              </p>
              <h3 className="text-2xl font-bold text-white">$2.5M</h3>
            </div>
            <div className="bg-purple-900/30 p-3 rounded-xl border border-purple-800/50">
              <Banknote className="text-purple-400 w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Today's Distribution Amount
              </p>
              <h3 className="text-2xl font-bold text-white">$60,000</h3>
            </div>
            <div className="bg-indigo-900/30 p-3 rounded-xl border border-indigo-800/50">
              <HandCoins className="text-indigo-400 w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Set Daily Profit Rate */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl mb-8">
        <div className="p-6 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-white mb-4">
            Set Daily Profit Rate
          </h3>
          <form onSubmit={handleSubmit} className="flex items-end gap-4">
            <div className="flex-1 max-w-xs">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Profit Percentage
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={profitRate}
                  onChange={(e) => setProfitRate(e.target.value)}
                  placeholder="Enter percentage"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  %
                </span>
              </div>
            </div>
            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-colors whitespace-nowrap"
            >
              Publish Daily Profit
            </button>
          </form>
        </div>
      </div>

      {/* Profit Distribution History */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl">
        <div className="px-6 py-4 border-b border-gray-700/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h3 className="text-lg font-semibold text-white">
            Profit Distribution History
          </h3>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search entries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-full sm:w-64"
              />
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
            <button className="px-4 py-2 bg-gray-700 text-white rounded-lg text-sm font-medium hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-colors whitespace-nowrap flex items-center gap-2">
              <Filter className="w-4 h-4" />
              Filter
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-700/30">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Profit Rate
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Total Amount
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Recipients
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50">
              {profitEntries.map((entry, index) => (
                <tr key={index} className="hover:bg-gray-700/30 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">{entry.date}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">{entry.rate}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">{entry.amount}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">{entry.recipients}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClasses(entry.statusColor)}`}>
                      {entry.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-3">
                      <button className="text-blue-400 hover:text-blue-300 transition-colors p-1">
                        <Eye className="w-5 h-5" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-300 transition-colors p-1">
                        <Edit className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-700/50 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-400">
            Showing <span className="font-medium text-white">1</span> to{" "}
            <span className="font-medium text-white">5</span> of{" "}
            <span className="font-medium text-white">30</span> results
          </div>
          <div className="flex space-x-2">
            <button className="px-3 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 transition-colors">
              Previous
            </button>
            <button className="px-3 py-2 border border-blue-600 bg-blue-600/20 text-blue-400 rounded-lg text-sm font-medium">
              1
            </button>
            <button className="px-3 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 transition-colors">
              2
            </button>
            <button className="px-3 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 transition-colors">
              3
            </button>
            <button className="px-3 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 transition-colors">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminProfit