import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoaderWithTextProps {
  isLoading: boolean;
  text?: string;
  overlay?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const LoaderWithText: React.FC<LoaderWithTextProps> = ({
  isLoading,
  text = "Loading...",
  overlay = false,
  size = 'md',
  className = ""
}) => {
  if (!isLoading) return null;

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const content = (
    <div className="flex flex-col items-center justify-center p-6">
      <Loader2 className={`${sizeClasses[size]} text-blue-400 animate-spin mb-3`} />
      <p className={`text-gray-300 ${textSizeClasses[size]} font-medium`}>{text}</p>
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-[#1A1F2E] rounded-xl shadow-lg border border-gray-700">
          {content}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center min-h-[200px] ${className}`}>
      {content}
    </div>
  );
};

export default LoaderWithText;