import { CloudUpload, Edit, ToggleRight, Trash2, Search } from 'lucide-react'
import React, { useState, ChangeEvent } from 'react'

interface Product {
  id: number
  name: string
  description: string
  image: string
  status: 'Active' | 'Inactive'
}

const AdminProducts: React.FC = () => {
  const [productName, setProductName] = useState<string>('')
  const [productDescription, setProductDescription] = useState<string>('')
  const [productImage, setProductImage] = useState<File | null>(null)
  const [searchTerm, setSearchTerm] = useState<string>('')

  const handleSubmit = (): void => {
    console.log('Adding product:', { 
      productName, 
      productDescription, 
      productImage: productImage?.name 
    })
    setProductName('')
    setProductDescription('')
    setProductImage(null)
  }

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0]
    if (file) {
      setProductImage(file)
    }
  }

  const products: Product[] = [
    {
      id: 1,
      name: "Avocados",
      description: "The world's first cryptocurrency, operating on a decentralized blockchain network.",
      image: "https://images.healthshots.com/healthshots/en/uploads/2024/04/04153309/avocado-1.jpg",
      status: "Active",
    },
    {
      id: 2,
      name: "Apple",
      description: "A leading technology company known for its innovative products including iPhone, Mac, and services.",
      image: "https://plus.unsplash.com/premium_photo-1661322640130-f6a1e2c36653?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YXBwbGV8ZW58MHx8MHx8fDA%3D",
      status: "Active",
    },
    {
      id: 3,
      name: "Bananas",
      description: "The most traded currency pair representing the exchange rate between the Euro and US Dollar.",
      image: "https://media.istockphoto.com/id/**********/photo/banana.jpg?s=612x612&w=0&k=20&c=NdHyi6Jd9y1855Q5mLO2tV_ZRnaJGtZGCSMMT7oxdF4=",
      status: "Active",
    },
    {
      id: 4,
      name: "Figs",
      description: "A precious metal used as a store of value and hedge against inflation.",
      image: "https://media.istockphoto.com/id/**********/photo/fresh-fig-fruit-and-slices-of-figs-background.jpg?s=612x612&w=0&k=20&c=XWqPYMQu13LUJUiwyNZ3tZCyarFlyRBwLWio_4mFARM=",
      status: "Active",
    },
  ]

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Add New Product Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg mb-6 border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-xl font-bold text-white mb-6">
            Add New Product
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Product Name
                </label>
                <input
                  type="text"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  placeholder="Enter product name"
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Product Image
                </label>
                <div className="flex items-center">
                  <div className="flex-1">
                    <div className="relative border border-gray-600 rounded-lg px-4 py-3 bg-gray-700">
                      <input
                        type="file"
                        onChange={handleImageChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        accept="image/*"
                      />
                      <div className="flex items-center text-gray-400 text-sm">
                        <CloudUpload className="mr-2 w-5 h-5 text-gray-300" />
                        <span>{productImage ? productImage.name : 'Choose file or drag & drop'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={productDescription}
                onChange={(e) => setProductDescription(e.target.value)}
                placeholder="Enter product description"
                rows={4}
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm resize-none"
              />
            </div>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleSubmit}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors whitespace-nowrap"
              >
                Add Product
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Product List Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700">
        <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
          <h3 className="text-xl font-bold text-white">
            Product List
          </h3>
          <div className="flex space-x-2">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search products..."
                className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm w-64"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-gray-700 rounded-lg border border-gray-600 overflow-hidden hover:shadow-xl hover:border-gray-500 transition-all duration-300"
            >
              <div className="aspect-w-4 aspect-h-3 relative">
                <div className="h-48 overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute top-2 right-2">
                  <span
                    className={`px-3 py-1 text-xs font-semibold rounded-full ${
                      product.status === "Active" 
                        ? "bg-green-900 text-green-300" 
                        : "bg-red-900 text-red-300"
                    }`}
                  >
                    {product.status}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h4 className="text-lg font-medium text-white mb-2">
                  {product.name}
                </h4>
                <p className="text-sm text-gray-400 mb-4 line-clamp-3">
                  {product.description}
                </p>
                <div className="flex justify-end space-x-2">
                  <button className="text-blue-400 hover:text-blue-300 transition-colors p-1">
                    <Edit className="w-5 h-5" />
                  </button>
                  <button className="text-red-400 hover:text-red-300 transition-colors p-1">
                    <Trash2 className="w-5 h-5" />
                  </button>
                  <button className="text-gray-400 hover:text-gray-300 transition-colors p-1">
                    <ToggleRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="px-6 py-4 border-t border-gray-700 flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Showing <span className="font-medium text-gray-300">1</span> to{" "}
            <span className="font-medium text-gray-300">{filteredProducts.length}</span> of{" "}
            <span className="font-medium text-gray-300">{filteredProducts.length}</span> results
          </div>
          <div className="flex space-x-2">
            <button className="px-4 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
              Previous
            </button>
            <button className="px-4 py-2 border border-blue-500 bg-blue-600 text-white rounded-lg text-sm font-medium">
              1
            </button>
            <button className="px-4 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminProducts